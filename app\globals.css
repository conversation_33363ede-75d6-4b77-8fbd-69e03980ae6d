@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@custom-variant dark (&:is(.dark *));

@theme {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-navy-blue: var(--navy-blue);

  --color-coral-500: var(--coral);
  --color-coral-600: var(--coral-600);

  --color-charcoal: var(--charcoal);

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));

  --color-chart-1: hsl(var(--chart-1));
  --color-chart-2: hsl(var(--chart-2));
  --color-chart-3: hsl(var(--chart-3));
  --color-chart-4: hsl(var(--chart-4));
  --color-chart-5: hsl(var(--chart-5));

  --color-sidebar: hsl(var(--sidebar-background));
  --color-sidebar-foreground: hsl(var(--sidebar-foreground));
  --color-sidebar-primary: hsl(var(--sidebar-primary));
  --color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
  --color-sidebar-accent: hsl(var(--sidebar-accent));
  --color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
  --color-sidebar-border: hsl(var(--sidebar-border));
  --color-sidebar-ring: hsl(var(--sidebar-ring));

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --font-montserrat: var(--font-montserrat), sans-serif;
  --font-roboto: var(--font-roboto), sans-serif;
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer utilities {
  :root {
    --navy-blue: #0f172a;
    --coral: #ff6b6b;
    --coral-600: #de5c5c;
    --charcoal: #333333;
  }

  /* Định nghĩa font mặc định cho body và một số thành phần khác */
  body {
    font-family: var(--font-roboto); /* Roboto là font mặc định cho nội dung */
    @apply text-base text-gray-700 leading-relaxed antialiased; /* Các thuộc tính chung cho nội dung */
  }

  /* Áp dụng font Montserrat cho các thẻ tiêu đề */
  h1 {
    font-family: var(--font-montserrat);
    @apply text-4xl md:text-5xl lg:text-6xl font-extrabold text-gray-900 leading-tight antialiased;
  }

  h2 {
    font-family: var(--font-montserrat);
    @apply text-3xl md:text-4xl font-bold text-gray-800 mb-6 antialiased;
  }

  h3 {
    font-family: var(--font-montserrat);
    @apply text-xl md:text-2xl font-semibold text-gray-800 mb-4 antialiased;
  }

  h4 {
    font-family: var(--font-montserrat);
    @apply text-lg font-medium text-gray-800 mb-2 antialiased;
  }

  /* h5 có thể là Roboto hoặc Montserrat tùy theo ngữ cảnh */
  h5 {
    font-family: var(
      --font-roboto
    ); /* Hoặc var(--font-montserrat) nếu muốn nhất quán với h1-h4 */
    @apply text-base font-medium text-gray-700 mb-1 antialiased;
  }

  h6 {
    font-family: var(--font-roboto);
    @apply text-sm font-normal text-gray-500 mt-2 antialiased;
  }

  /* Các thành phần khác */

  /* Nút bấm (nếu bạn muốn có một style mặc định chung cho các nút) */
  button {
    font-family: var(--font-montserrat);
    @apply font-semibold; /* Áp dụng trọng lượng SemiBold mặc định */
  }

  button.dropdown-select {
    font-family: var(--font-roboto);
    @apply font-normal; /* Áp dụng trọng lượng Medium cho dropdown select */
    @apply text-gray-500;
  }

  /* Thẻ a trong navigation (ví dụ) */
  nav a {
    font-family: var(--font-montserrat);
    @apply text-[13.5px] font-medium antialiased; /* Kích thước và trọng lượng cho nav bar */
  }

  /* Input fields */
  input,
  textarea,
  select {
    font-family: var(--font-roboto);
    @apply text-base antialiased; /* Font cho input fields */
  }

  /* Placeholder */
  ::placeholder {
    font-family: var(--font-roboto);
    @apply text-gray-400;
  }

  body > div[data-portal='true'] {
    pointer-events: auto !important;
  }

  /* global.css */
  body {
    pointer-events: auto !important;
  }

  /* Style cho container chính của marker */
  .custom-price-marker {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: absolute;
  }

  /* Container cho marker và highlight icon */
  .custom-price-marker .marker-container {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  /* Highlight icon */
  .custom-price-marker .highlight-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
  }

  /* Style cho phần "bong bóng" hiển thị giá - Default */
  .custom-price-marker .price-bubble {
    background-color: #2a2d4a;
    color: white;
    padding: 2px 7px;
    border-radius: 10px;
    font-size: 11px;
    text-align: center;
    white-space: nowrap;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    pointer-events: auto;
  }

  /* Style cho Sale markers */
  .custom-price-marker.marker-sale .price-bubble {
    background-color: var(--color-coral-600);
    color: white;
  }

  .custom-price-marker.marker-sale .price-arrow {
    border-top: 8px solid var(--color-coral-600);
  }

  .custom-price-marker.marker-sale:hover .price-bubble {
    background-color: var(--color-coral-600);
  }

  .custom-price-marker.marker-sale:hover .price-arrow {
    border-top: 8px solid var(--color-coral-600);
  }

  /* Style cho Rent markers */
  .custom-price-marker.marker-rent .price-bubble {
    background-color: #14b8a6; /* teal-500 */
    color: white;
  }

  .custom-price-marker.marker-rent .price-arrow {
    border-top: 8px solid #14b8a6;
  }

  .custom-price-marker.marker-rent:hover .price-bubble {
    background-color: #0d9488; /* teal-600 */
  }

  .custom-price-marker.marker-rent:hover .price-arrow {
    border-top: 8px solid #0d9488;
  }

  /* Style cho phần tạo hình tam giác (mũi tên) */
  .custom-price-marker .price-arrow {
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #2a2d4a; /* Default color */
    pointer-events: none;
  }

  /* Legacy highlighted styles - kept for backward compatibility */
  .custom-price-marker.highlighted .price-bubble {
    font-weight: bold;
  }

  .home-map .mapboxgl-popup-content {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
  }

  .home-map .mapboxgl-popup-anchor-bottom .mapboxgl-popup-tip {
    border: none !important;
    border-top-color: transparent !important;
    display: none;
  }

  .mapboxgl-popup-anchor-top .mapboxgl-popup-tip {
    border: none !important;
    border-bottom-color: transparent !important;
    display: none;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 0 100% 71%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Custom collapsible animations */
  @keyframes collapsible-down {
    from {
      height: 0;
      opacity: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
      opacity: 1;
    }
  }

  @keyframes collapsible-up {
    from {
      height: var(--radix-collapsible-content-height);
      opacity: 1;
    }
    to {
      height: 0;
      opacity: 0;
    }
  }

  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slide-in {
    from {
      transform: translateX(-10px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .animate-collapsible-down {
    animation: collapsible-down 0.3s ease-in-out;
  }

  .animate-collapsible-up {
    animation: collapsible-up 0.3s ease-in-out;
  }

  .animate-fade-in {
    animation: fade-in 0.4s ease-in-out 0.1s both;
  }

  .animate-slide-in {
    animation: slide-in 0.3s ease-in-out;
  }

  /* Enhanced hover and focus states */
  .collapsible-trigger:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .collapsible-trigger:active {
    transform: translateY(0);
  }

  /* Smooth transitions for all interactive elements */
  .smooth-transition {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Additional CSS fixes for badge positioning */

/* Ensure badges don't interfere with tab content */
.notification-tab-trigger {
  position: relative;
  overflow: visible; /* Important: allows badges to extend outside */
}

/* Badge positioning fix */
.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  z-index: 10;
  pointer-events: none; /* Prevents badge from interfering with clicks */

  /* Ensure badge is always visible */
  min-width: 16px;
  height: 16px;

  /* Better typography */
  font-size: 10px;
  font-weight: 600;
  line-height: 1;

  /* Visual improvements */
  background-color: #ef4444; /* red-500 */
  color: white;
  border: 1px solid white;
  border-radius: 50%;

  /* Center content */
  display: flex;
  align-items: center;
  justify-content: center;

  /* Smooth transitions */
  transition: all 0.2s ease-in-out;
}

/* Handle larger numbers */
.notification-badge--large {
  min-width: 20px;
  height: 16px;
  border-radius: 8px;
  padding: 0 4px;
}

/* Animation for new notifications */
@keyframes badge-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.notification-badge--new {
  animation: badge-pulse 0.6s ease-in-out;
}

/* Ẩn icon reveal mặc định trong Edge/Chrome */
input[type="password"]::-ms-reveal,
input[type="password"]::-ms-clear {
  display: none;
}

input[type="password"]::-webkit-credentials-auto-fill-button {
  visibility: hidden;
  display: none !important;
  pointer-events: none;
  position: absolute;
  right: 0;
}

/* Property Tags */
.tag-ban {
  background-color: var(--color-coral-600);
  color: white;
}

.tag-thue {
  background-color: #14b8a6; /* teal-500 */
  color: white;
}

/* Price styling */
.price-text {
  color: var(--color-coral-600);
  font-weight: 600;
  font-family: var(--font-montserrat);
}