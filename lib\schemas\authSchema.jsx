import { z } from "zod"

export const registerSchema = z.object({
  fullname: z.string().min(3, "<PERSON><PERSON> tên phải có ít nhất 3 ký tự"),
  email: z.string().email("Định dạng email không đúng"),
  phone: z.string().min(10, "Số điện thoại không hợp lệ"),
  userType: z.enum(["Buyer", "Seller"], { required_error: "Chọn 1 trong 2" }),
  password: z.string().min(6, "Mật khẩu phải có ít nhất 6 ký tự"),
  confirmPassword: z.string().min(6, "<PERSON>ui lòng xác nhận mật khẩu"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Mật khẩu không trùng khớp",
  path: ["confirmPassword"], 
})

export const loginSchema = z.object({
  email: z.string({ required_error: "Bắt buộc nhập." }).email("Định dạng email không đúng"),
  password: z.string({ required_error: "Bắt buộc nhập." }).min(6, "Mật khẩu phải có ít nhất 6 ký tự"),
})

export const changePasswordSchema = z.object({
  oldPassword: z.string().min(1, "Vui lòng nhập mật khẩu cũ"),
  newPassword: z.string().min(6, "Mật khẩu mới phải có ít nhất 6 ký tự"),
  confirmPassword: z.string().min(6, "Vui lòng xác nhận mật khẩu mới"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Mật khẩu mới không trùng khớp",
  path: ["confirmPassword"],
});

export const forgetPasswordSchema = z.object({
  email: z.string({required_error: "Vui lòng nhập địa chỉ mail"}).email("Định dạng email không đúng"),
});
  
export const resetPasswordSchema = z.object({
  userId: z.string().min(1, "Vui lòng nhập User ID"),
  token: z.string().min(1, "Vui lòng nhập Token"),
  newPassword: z.string().min(6, "Mật khẩu mới phải có ít nhất 6 ký tự"),
  confirmPassword: z.string().min(6, "Vui lòng xác nhận mật khẩu mới"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Mật khẩu mới không trùng khớp",
  path: ["confirmPassword"],
});