"use server";

import { handleErrorResponse, logError } from "@/lib/apiUtils";
import { fetchWithAuth, getSession, fetchWithoutAuth } from "@/lib/sessionUtils";
import { parseEmptyStringsToNull } from "@/lib/utils";

const API_BASE_URL = `${process.env.API_URL}/api/Property`;
const PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;

// New function to search properties with filters
export async function searchProperties(filterCriteria) {
  // Build query parameters from filter criteria
  const queryParams = new URLSearchParams();

  // Transaction Type (postType)
  if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {
    filterCriteria.transactionType.forEach((type) => {
      queryParams.append("postType", type);
    });
  }

  // Property Type
  if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {
    filterCriteria.propertyType.forEach((type) => {
      queryParams.append("propertyType", type);
    });
  }

  // Location
  if (filterCriteria.location) {
    if (filterCriteria.location.province) {
      queryParams.append("cityId", filterCriteria.location.province);
    }
    if (filterCriteria.location.district) {
      queryParams.append("districtId", filterCriteria.location.district);
    }
    if (filterCriteria.location.address) {
      queryParams.append("address", filterCriteria.location.address);
    }
  }

  // Price Range
  if (filterCriteria.priceRange) {
    if (filterCriteria.priceRange.min) {
      queryParams.append("minPrice", filterCriteria.priceRange.min);
    }
    if (filterCriteria.priceRange.max) {
      queryParams.append("maxPrice", filterCriteria.priceRange.max);
    }
  }

  // Area Range
  if (filterCriteria.areaRange) {
    if (filterCriteria.areaRange.min) {
      queryParams.append("minArea", filterCriteria.areaRange.min);
    }
    if (filterCriteria.areaRange.max) {
      queryParams.append("maxArea", filterCriteria.areaRange.max);
    }
  }

  // Bedrooms
  if (filterCriteria.bedrooms) {
    queryParams.append("minRooms", filterCriteria.bedrooms);
  }

  // Bathrooms
  if (filterCriteria.bathrooms) {
    queryParams.append("minToilets", filterCriteria.bathrooms);
  }

  // Direction
  if (filterCriteria.direction) {
    queryParams.append("direction", filterCriteria.direction);
  }

  // Legal Status
  if (filterCriteria.legalStatus) {
    queryParams.append("legality", filterCriteria.legalStatus);
  }

  // Road Width
  if (filterCriteria.roadWidth) {
    queryParams.append("minRoadWidth", filterCriteria.roadWidth);
  }

  // User location for proximity search
  if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {
    queryParams.append("swLat", filterCriteria.sw_lat);
    queryParams.append("swLng", filterCriteria.sw_lng);
    queryParams.append("neLat", filterCriteria.ne_lat);
    queryParams.append("neLng", filterCriteria.ne_lng);
  }

  // Build the URL with query parameters
  const url = `${API_BASE_URL}/search?${queryParams.toString()}`;

  // Use fetchWithoutAuth for public API call
  const response = await fetchWithoutAuth(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  return response;
}

export async function getPropertyById(propertyId) {
  const url = `${API_BASE_URL}/${propertyId}`;
  // Use fetchWithoutAuth for public API call
  const response = await fetchWithoutAuth(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
  return response;
}

export async function getPropertyBySlug(slug) {
  const url = `${API_BASE_URL}/slug/${slug}`;
  // Use fetchWithoutAuth for public API call
  const response = await fetchWithoutAuth(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
  return response;
}

export async function updatePropertyById(prevState, formData) {
  // Get token from formData
  const propertyId = formData.get("propertyId");

  // Convert FormData to a plain object for easier handling
  const formDataObject = Object.fromEntries(formData.entries());
  const payload = {
    ...formDataObject,
  };

  const userSession = await getSession("UserProfile");
  if (userSession) {
    const user = JSON.parse(userSession);
    payload.ownerId = user.id;
  }

  // ✅ Parse the uploadedFiles JSON string back into an array
  if (formDataObject.UploadedFiles) {
    payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);
  }

  payload.isHighlighted = formDataObject.isHighlighted === "true" ? true : false;
  payload.isAutoRenew = formDataObject.isAutoRenew === "true" ? true : false;

  return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {
    method: "PUT",
    body: JSON.stringify(parseEmptyStringsToNull(payload)),
    headers: {
      "Content-Type": "application/json",
      accept: "*/*",
    },
  });
}

export async function deletePropertyById(propertyId) {
  return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export async function createProperty(prevState, formData) {
  // Convert FormData to a plain object for easier handling
  const formDataObject = Object.fromEntries(formData.entries());

  const payload = {
    ...formDataObject,
  };

  const userSession = await getSession("UserProfile");
  if (userSession) {
    const user = JSON.parse(userSession);
    payload.ownerId = user.id;
  }

  // ✅ Parse the uploadedFiles JSON string back into an array
  if (formDataObject.UploadedFiles) {
    payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);
  }

  payload.isHighlighted = formDataObject.isHighlighted === "true" ? true : false;
  payload.isAutoRenew = formDataObject.isAutoRenew === "true" ? true : false;

  return await fetchWithAuth(API_BASE_URL, {
    method: "POST",
    body: JSON.stringify(parseEmptyStringsToNull(payload)),
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export async function updatePropertyStatus(formData) {
  const propertyId = formData.get("propertyId");

  return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      status: formData.get("status"),
      comment: formData.get("comment") || "",
    }),
  });
}

export async function getPropertyByUser(status, page = 1, pageSize = 30) {
  // If status is 'counts', call the stats endpoint instead
  if (status === "counts") {
    return await getPropertyStats();
  }

  let url = `${API_BASE_URL}/me`;
  const queryParams = new URLSearchParams();

  // Add parameters if provided
  if (status) {
    queryParams.append("status", status);
  }

  queryParams.append("page", page);
  queryParams.append("pageSize", pageSize);

  // Append query parameters to URL if any exist
  if (queryParams.toString()) {
    url += `?${queryParams.toString()}`;
  }

  const response = await fetchWithAuth(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  return response;
}

/**
 * Get property statistics by status
 * @returns {Promise<Object>} Response with property stats data
 */
export async function getPropertyStats() {
  const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (response?.isSuccess) {
    // Transform the response to match the expected format for the UI
    const statsData = response.data;
    return {
      isSuccess: true,
      data: {
        total: statsData.totalProperties || 0,
        approved: statsData.propertiesByStatus?.Approved || 0,
        pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,
        rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,
        rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,
        waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,
        expired: statsData.propertiesByStatus?.Expired || 0,
        draft: statsData.propertiesByStatus?.Draft || 0,
        sold: statsData.propertiesByStatus?.Sold || 0,
      },
    };
  }

  return response;
}

export async function uploadPropertyImages(prevState, formData) {
  const files = formData.getAll("files"); // Get all files from FormData
  const propertyId = formData.get("propertyId");

  if (files.length === 0) {
    return handleErrorResponse(false, null, "Thiếu tập tin");
  }

  const formDataToSend = new FormData();
  files.forEach((file) => formDataToSend.append("files", file));
  formDataToSend.append("propertyId", propertyId);

  return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {
    method: "POST",
    body: formDataToSend,
  });
}

export async function verifyPropertyRemainingTimes(propertyId) {
  return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {
    method: "GET",
  });
}

export async function getPropertyStatusHistory(propertyId) {
  return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {
    method: "GET",
  });
}

export async function getNearbyProperties(latitude, longitude, radius = 5000) {
  // Use fetchWithoutAuth for public API call
  return await fetchWithoutAuth(`${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {
    method: "GET",
  });
}

// Get property report data from PropertyEngagementSummary
export async function getPropertyReportById(propertyId) {
  // Call the real API endpoint to get property engagement summary
  const response = await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/property/${propertyId}/summary`);

  return response;
}

// Update property media caption
export async function updatePropertyMediaCaption(mediaId, caption) {
  const payload = {
    id: mediaId,
    caption: caption,
  };

  return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {
    method: "PUT",
    body: JSON.stringify(payload),
    headers: {
      "Content-Type": "application/json",
    },
  });
}

// Update property media isAvatar status
export async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {
  const payload = {
    id: mediaId,
    isAvatar: isAvatar,
  };

  return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {
    method: "PUT",
    body: JSON.stringify(payload),
    headers: {
      "Content-Type": "application/json",
    },
  });
}

// Delete property media
export async function deletePropertyMedia(mediaId) {
  return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

/**
 * Bulk delete multiple properties
 * @param {Array<string>} propertyIds - Array of property IDs to delete
 * @returns {Promise<Object>} Response with success/error information
 */
export async function bulkDeleteProperties(propertyIds) {
  return await fetchWithAuth(`${API_BASE_URL}/bulk`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      propertyIds: propertyIds,
    }),
  });
}

/**
 * Update highlight status for a property
 * @param {string} propertyId - ID of the property to update
 * @param {boolean} isHighlighted - Whether the property should be highlighted
 * @returns {Promise<Object>} Response with success/error information
 */
export async function updatePropertyHighlight(propertyId, isHighlighted) {
  return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      isHighlighted: isHighlighted,
    }),
  });
}

/**
 * Bulk update highlight status for multiple properties with payment processing
 * @param {Array<string>} propertyIds - Array of property IDs to update
 * @param {boolean} isHighlighted - Whether the properties should be highlighted
 * @returns {Promise<Object>} Response with detailed success/error information
 */
export async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {
  const response = await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      propertyIds: propertyIds,
      isHighlighted: isHighlighted,
    }),
  });

  if (!response?.isSuccess) {
    return response;
  } else {
    // Success response - extract data from response.data
    const data = response.data;
    return {
      isSuccess: data.isSuccess,
      message: data.message || "Thao tác thành công.",
      totalProcessed: data.totalProcessed || 0,
      isSuccessfullyHighlighted: data.isSuccessfullyHighlighted || 0,
      alreadyHighlighted: data.alreadyHighlighted || 0,
      failed: data.failed || 0,
      totalCost: data.totalCost || 0,
      propertyResults: data.propertyResults || [],
    };
  }
}

/**
 * Renew property for specified duration
 * @param {string} propertyId - ID of the property to renew
 * @param {number} durationInDays - Number of days to extend the property
 * @returns {Promise<Object>} Response with success/error information
 */
export async function renewProperty(propertyId, durationInDays) {
  return await fetchWithAuth(`${API_BASE_URL}/renew`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      propertyId: propertyId,
      durationInDays: durationInDays,
    }),
  });
}