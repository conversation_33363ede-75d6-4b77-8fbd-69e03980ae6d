import { Home, FileText, CheckCircle, XCircle, Clock } from "lucide-react";
import { Link } from '@/i18n/navigation';

export default function PropertyStats({ propertyStats }) {
  // Handle case where propertyStats might be null or undefined
  if (!propertyStats) {
    return (
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-5 gap-4">
        <div className="flex flex-col items-center justify-center p-3 bg-gray-50 rounded-md">
          <div className="text-gray-400">No data available</div>
        </div>
      </div>
    );
  }

  const stats = [
    {
      label: "Tổng số BĐS",
      value: propertyStats.totalProperties || 0,
      icon: Home,
      color: "text-navy-blue",
      bgColor: "bg-navy-blue",
      lightBgColor: "bg-blue-50",
      status: null // No filter for total
    },
    {
      label: "Đ<PERSON> duyệt",
      value: propertyStats.approvedProperties || 0,
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-600",
      lightBgColor: "bg-green-50",
      status: "Approved"
    },
    {
      label: "Đã hết hạn",
      value: propertyStats.expiredProperties || 0,
      icon: Clock,
      color: "text-orange-600",
      bgColor: "bg-orange-600",
      lightBgColor: "bg-orange-50",
      status: "Expired"
    },
    {
      label: "Bản nháp",
      value: propertyStats.draftProperties || 0,
      icon: FileText,
      color: "text-gray-600",
      bgColor: "bg-gray-600",
      lightBgColor: "bg-gray-50",
      status: "Draft"
    },
    {
      label: "Bị từ chối",
      value: propertyStats.rejectedByAdminProperties || 0,
      icon: XCircle,
      color: "text-red-600",
      bgColor: "bg-red-600",
      lightBgColor: "bg-red-50",
      status: "RejectedByAdmin"
    }
  ];

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-5 gap-4">
      {stats.map((stat, index) => {
        const StatCard = ({ children }) => {
          if (stat.status) {
            return (
              <Link
                href={`/user/bds?status=${stat.status}`}
                className="flex flex-col items-center justify-center p-3 rounded-md transition-all duration-200 hover:shadow-md hover:scale-105 cursor-pointer"
                style={{ backgroundColor: stat.lightBgColor }}
              >
                {children}
              </Link>
            );
          }
          return (
            <div
              className="flex flex-col items-center justify-center p-3 rounded-md"
              style={{ backgroundColor: stat.lightBgColor }}
            >
              {children}
            </div>
          );
        };

        return (
          <StatCard key={index}>
            <div className={`p-2 rounded-full mb-2 ${stat.lightBgColor}`}>
              <stat.icon className={`h-5 w-5 ${stat.color}`} />
            </div>
            <span className="text-2xl font-bold text-gray-800">{stat.value}</span>
            <span className="text-xs text-gray-600 text-center font-medium">{stat.label}</span>
          </StatCard>
        );
      })}
    </div>
  );
}