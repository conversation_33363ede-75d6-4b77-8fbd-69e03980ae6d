import { Progress } from "@/components/ui/progress";

export default function MemberRanking({ memberRanking }) {
  // English to Vietnamese rank mapping
  const rankMapping = {
    'Bronze': 'Đồng',
    'Silver': 'Bạc',
    'Gold': 'Vàng',
    'Platinum': 'B<PERSON><PERSON> kim',
    'Diamond': '<PERSON>',
    'Regular': 'Th<PERSON>ờng'
  };

  // Colors for Vietnamese rank names
  const rankColors = {
    'Đồng': 'text-orange-600',
    'Bạc': 'text-gray-400',
    'Vàng': 'text-amber-500',
    'Bạch kim': 'text-slate-400',
    '<PERSON> c<PERSON>': 'text-blue-500',
    'Thường': 'text-gray-600'
  };

  // Get Vietnamese rank name from English
  const getCurrentRankName = () => rankMapping[memberRanking.currentRank] || 'Thường';
  const getNextRankName = () => memberRanking.nextRank ? rankMapping[memberRanking.nextRank] : null;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className={`text-3xl font-bold ${rankColors[getCurrentRankName()] || 'text-gray-700'}`}>
            {getCurrentRankName()}
          </div>
          <div className="text-sm text-gray-500">Hạng thành viên hiện tại</div>
        </div>
      </div>

      {memberRanking.nextRank && (
        <div className="mt-4">
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm text-gray-500">Tiến độ lên hạng {getNextRankName()}</span>
            <span className="text-sm font-medium">{memberRanking.progressPercentage}%</span>
          </div>
          <Progress value={memberRanking.progressPercentage} className="h-2" />
          
          <div className="flex justify-between mt-1 text-xs text-gray-500">
            <span>{new Intl.NumberFormat('vi-VN').format(memberRanking.minSpent)} ₫</span>
            <span>{new Intl.NumberFormat('vi-VN').format(memberRanking.maxSpent)} ₫</span>
          </div>
          
          <div className="mt-3 text-sm text-center">
            Cần chi tiêu thêm <span className="font-semibold text-coral-500">
              {new Intl.NumberFormat('vi-VN').format(memberRanking.spendingToNextRank)} ₫
            </span> để lên hạng {getNextRankName()}
          </div>
        </div>
      )}

      <div className="text-xs text-gray-500 mt-2 text-center">
        Các đặc quyền hạng thành viên sẽ được áp dụng tự động dựa trên tổng chi tiêu của bạn.
      </div>
    </div>
  );
} 