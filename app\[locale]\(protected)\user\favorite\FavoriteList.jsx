"use client";

import { useState, useEffect, useCallback } from "react";
import { useTranslations } from "next-intl";
import { Loader2, Filter, SortAsc, SortDesc, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { getUserFavoritesWithDetails, removeFromFavorites } from "@/app/actions/server/userFavorite";
import FavoritePropertyCard from "@/components/favorite/FavoritePropertyCard";
import Pagination from "@/components/property/Pagination";
import NoData from "@/components/layout/NoData";

export default function FavoriteList() {
  const t = useTranslations("FavoriteList");
  const { toast } = useToast();

  // State management
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    totalCount: 0,
    pageCount: 1,
    currentPage: 1,
    pageSize: 30,
    hasNextPage: false,
    hasPreviousPage: false
  });

  // Filter state
  const [filters, setFilters] = useState({
    minPrice: "",
    maxPrice: "",
    fromDate: "",
    toDate: "",
    sortBy: "CreatedAt",
    sortDescending: true,
    page: 1,
    pageSize: 30
  });

  const [showFilters, setShowFilters] = useState(false);

  // Fetch favorites data
  const fetchFavorites = useCallback(async (currentFilters = filters) => {
    setLoading(true);
    try {
      const filterParams = {
        ...currentFilters,
        minPrice: currentFilters.minPrice ? parseFloat(currentFilters.minPrice) : undefined,
        maxPrice: currentFilters.maxPrice ? parseFloat(currentFilters.maxPrice) : undefined,
        fromDate: currentFilters.fromDate || undefined,
        toDate: currentFilters.toDate || undefined,
      };

      const result = await getUserFavoritesWithDetails(filterParams);
      
      if (result?.isSuccess && result.data) {
        setFavorites(result.data.items || []);
        setPagination({
          totalCount: result.data.totalCount || 0,
          pageCount: result.data.pageCount || 1,
          currentPage: result.data.currentPage || 1,
          pageSize: result.data.pageSize || 30,
          hasNextPage: result.data.hasNextPage || false,
          hasPreviousPage: result.data.hasPreviousPage || false
        });
      } else {
        toast({
          description: result.message || t("errorFetchingFavorites", { defaultValue: "Không thể tải danh sách yêu thích" }),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching favorites:", error);
      toast({
        description: t("errorFetchingFavorites", { defaultValue: "Không thể tải danh sách yêu thích" }),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [filters, toast, t]);

  // Initial load
  useEffect(() => {
    fetchFavorites();
  }, []);

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    const updatedFilters = { ...filters, ...newFilters, page: 1 };
    setFilters(updatedFilters);
    fetchFavorites(updatedFilters);
  };

  // Handle page change
  const handlePageChange = (page) => {
    const updatedFilters = { ...filters, page };
    setFilters(updatedFilters);
    fetchFavorites(updatedFilters);
  };

  // Handle remove from favorites
  const handleRemoveFavorite = async (propertyId) => {
    try {
      const result = await removeFromFavorites(propertyId);
      if (result?.isSuccess) {
        toast({
          description: t("removedFromFavorites", { defaultValue: "Đã xóa khỏi danh sách yêu thích" }),
        });
        // Refresh the list
        fetchFavorites();
        // Dispatch event to update navbar count
        window.dispatchEvent(new CustomEvent("favorites-changed"));
      } else {
        toast({
          description: result.message || t("errorRemovingFavorite", { defaultValue: "Không thể xóa khỏi danh sách yêu thích" }),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error removing favorite:", error);
      toast({
        description: t("errorRemovingFavorite", { defaultValue: "Không thể xóa khỏi danh sách yêu thích" }),
        variant: "destructive",
      });
    }
  };

  // Clear filters
  const clearFilters = () => {
    const defaultFilters = {
      minPrice: "",
      maxPrice: "",
      fromDate: "",
      toDate: "",
      sortBy: "CreatedAt",
      sortDescending: true,
      page: 1,
      pageSize: 12
    };
    setFilters(defaultFilters);
    fetchFavorites(defaultFilters);
  };

  // Check if filters are active
  const hasActiveFilters = filters.minPrice || filters.maxPrice || filters.fromDate || filters.toDate;

  return (
    <div className="space-y-6">
      {/* Filter and Sort Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-2">
          <Popover open={showFilters} onOpenChange={setShowFilters}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Filter className="h-4 w-4" />
                {t("filters", { defaultValue: "Bộ lọc" })}
                {hasActiveFilters && (
                  <Badge variant="primary" rounded="full" className="ml-1">
                    {[filters.minPrice, filters.maxPrice, filters.fromDate, filters.toDate].filter(Boolean).length}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="start">
              <div className="space-y-4">
                <h4 className="font-medium">{t("filterOptions", { defaultValue: "Tùy chọn lọc" })}</h4>
                
                {/* Price Range */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-1">
                    
                    <img src="/vnd.png" alt="VND" className="h-3 w-3 rounded-full" />
                    {t("priceRange", { defaultValue: "Khoảng giá" })}
                  </Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="number"
                      placeholder={t("minPrice", { defaultValue: "Giá tối thiểu" })}
                      value={filters.minPrice}
                      onChange={(e) => setFilters(prev => ({ ...prev, minPrice: e.target.value }))}
                    />
                    <Input
                      type="number"
                      placeholder={t("maxPrice", { defaultValue: "Giá tối đa" })}
                      value={filters.maxPrice}
                      onChange={(e) => setFilters(prev => ({ ...prev, maxPrice: e.target.value }))}
                    />
                  </div>
                </div>

                {/* Date Range */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {t("dateRange", { defaultValue: "Khoảng thời gian" })}
                  </Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="date"
                      value={filters.fromDate}
                      onChange={(e) => setFilters(prev => ({ ...prev, fromDate: e.target.value }))}
                    />
                    <Input
                      type="date"
                      value={filters.toDate}
                      onChange={(e) => setFilters(prev => ({ ...prev, toDate: e.target.value }))}
                    />
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 pt-2">
                  <Button 
                    onClick={() => handleFilterChange(filters)} 
                    className="flex-1"
                    size="sm"
                  >
                    {t("applyFilters", { defaultValue: "Áp dụng" })}
                  </Button>
                  <Button 
                    onClick={clearFilters} 
                    variant="outline" 
                    size="sm"
                  >
                    {t("clearFilters", { defaultValue: "Xóa" })}
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* Sort Controls */}
        <div className="flex items-center gap-2">
          <Select
            value={filters.sortBy}
            onValueChange={(value) => handleFilterChange({ sortBy: value })}
          >
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="CreatedAt">{t("sortByDate", { defaultValue: "Ngày thêm" })}</SelectItem>
              <SelectItem value="Price">{t("sortByPrice", { defaultValue: "Giá" })}</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleFilterChange({ sortDescending: !filters.sortDescending })}
            className="gap-1"
          >
            {filters.sortDescending ? <SortDesc className="h-4 w-4" /> : <SortAsc className="h-4 w-4" />}
            {filters.sortDescending ? t("descending", { defaultValue: "Giảm dần" }) : t("ascending", { defaultValue: "Tăng dần" })}
          </Button>
        </div>
      </div>

      {/* Results Summary */}
      <div className="text-sm text-gray-600">
        {t("resultsCount", { 
          count: pagination.totalCount,
          defaultValue: `Tìm thấy ${pagination.totalCount} bất động sản yêu thích`
        })}
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-teal-500" />
        </div>
      )}

      {/* Empty State */}
      {!loading && favorites.length === 0 && (
        <NoData
          hasCreateButton={false}
          createMessage={t("noFavorites", { defaultValue: "Bạn chưa có bất động sản yêu thích nào" })}
        />
      )}

      {/* Favorites Grid */}
      {!loading && favorites.length > 0 && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {favorites.map((favorite) => (
              <FavoritePropertyCard
                key={favorite.id}
                favorite={favorite}
                onRemove={handleRemoveFavorite}
              />
            ))}
          </div>

          {/* Pagination */}
          {pagination.pageCount > 1 && (
            <Pagination
              pagination={pagination}
              onPageChange={handlePageChange}
            />
          )}
        </>
      )}
    </div>
  );
}
