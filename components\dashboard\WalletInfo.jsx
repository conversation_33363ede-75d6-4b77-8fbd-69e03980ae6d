import { Card, CardContent } from "@/components/ui/card";
import { PiggyBank, TrendingUp, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import { formatCurrency } from "@/lib/utils";
import {Link} from '@/i18n/navigation';;

export default function WalletInfo({ walletInfo, showActions = true }) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex flex-col">
          <span className="text-sm text-gray-500">Số dư</span>
          <span className="text-2xl font-bold text-navy-blue">
            {formatCurrency(walletInfo.balance)}
          </span>
        </div>
        {showActions && (
          <Button className="bg-teal-500 hover:bg-teal-600 text-white" size="sm" asChild>
            <Link href="/user/wallet">
              Nạp tiền
            </Link>
          </Button>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4 mt-4">
        <div className="flex flex-col">
          <span className="text-sm text-gray-500 flex items-center">
            <TrendingUp className="w-4 h-4 mr-1" />
            Tổng chi tiêu
          </span>
          <span className="text-lg font-semibold">
            {formatCurrency(walletInfo.totalSpent)}
          </span>
        </div>
        <div className="flex flex-col">
          <span className="text-sm text-gray-500 flex items-center">
            <Calendar className="w-4 h-4 mr-1" />
            Chi tiêu tháng trước
          </span>
          <span className="text-lg font-semibold">
            {formatCurrency(walletInfo.lastMonthSpending)}
          </span>
        </div>
      </div>

      <div className="text-sm text-gray-500 mt-2">
        Tổng số giao dịch: <span className="font-medium">{walletInfo.totalTransactions}</span>
      </div>

      {showActions && (
        <div className="pt-3">
          <Button variant="outline" size="sm" className="w-full" asChild>
            <Link href="/user/transactions">
              Lịch sử giao dịch
            </Link>
          </Button>
        </div>
      )}
    </div>
  );
} 