"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { formatCurrency } from "@/lib/utils"

export function AmountSelection({ amount, setAmount, presetAmounts, t }) {
  const [customAmount, setCustomAmount] = useState("")

  // Handle custom amount input
  const handleCustomAmountChange = (e) => {
    const value = e.target.value.replace(/\D/g, "")
    setCustomAmount(value)
    if (value) {
      setAmount(Number.parseInt(value))
    } else {
      setAmount(0)
    }
  }

  // Handle preset amount selection
  const handlePresetAmount = (presetAmount) => {
    setAmount(presetAmount)
    setCustomAmount("")
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="space-y-4">
        <div>
          <Label htmlFor="amount" className="text-base">
            {t("amountLabel")}
          </Label>
          <div className="grid grid-cols-3 gap-2 mt-2">
            {presetAmounts.map((presetAmount) => (
              <Button
                key={presetAmount.value}
                type="button"
                variant={amount === presetAmount.value ? "default" : "outline-solid"}
                className={`h-12 border border-gray-300 ${amount === presetAmount.value ? "bg-teal-600 hover:bg-teal-700 border-teal-600" : "hover:border-gray-400"}`}
                onClick={() => handlePresetAmount(presetAmount.value)}
              >
                {formatCurrency(presetAmount.value)}
              </Button>
            ))}
          </div>
        </div>

        <div className="relative">
          <Label htmlFor="customAmount" className="text-base">
            {t("amountLabel")}
          </Label>
          <div className="relative mt-2">
            <Input
              id="customAmount"
              value={customAmount}
              onChange={handleCustomAmountChange}
              className="pl-12 h-12 text-lg"
              placeholder={t("customAmountPlaceholder")}
            />
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">₫</div>
          </div>
        </div>

        <div className="pt-4">
          <div className="flex justify-between text-sm text-gray-500">
            <span>{t("depositAmount")}</span>
            <span className="font-medium text-black">{isNaN(amount) ? "0₫" : formatCurrency(amount)}</span>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
