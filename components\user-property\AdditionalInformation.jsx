import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import React from "react";
import { useTranslations } from "next-intl";
import { Card } from "../ui/card";
import { CollapsibleSection } from "../ui/collapsible-section";

export default function AdditionalInformation({ form, isFormDisabled }) {
  const t = useTranslations("AdditionalInformation");

  return (
    <Card className="border hover:border-blue-200 transition-all duration-300 mb-3">
      <CollapsibleSection title={t("detailedInformation")} subTitle={t("optional")}>
        <Separator className="mb-6" />
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-3">
          <FormField
            control={form.control}
            name="area"
            className="mt-3"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("area")}</FormLabel>
                <FormControl>
                  <Input placeholder="VD: 100" {...field} type="number" suffix="m²" disabled={isFormDisabled} readOnly={isFormDisabled} />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="floors"
            className="mt-3"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("floors")}</FormLabel>
                <FormControl>
                  <Input placeholder="VD: 3" {...field} type="number" disabled={isFormDisabled} readOnly={isFormDisabled} />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="rooms"
            className="mt-3"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("rooms")}</FormLabel>
                <FormControl>
                  <Input placeholder="VD: 3" {...field} type="number" disabled={isFormDisabled} readOnly={isFormDisabled} />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="toilets"
            className="mt-3"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("toilets")}</FormLabel>
                <FormControl>
                  <Input placeholder="VD: 3" {...field} type="number" disabled={isFormDisabled} readOnly={isFormDisabled} />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
          <FormField
            control={form.control}
            name="direction"
            render={({ field }) => (
              <FormItem className="mt-3">
                <FormLabel>{t("direction")}</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isFormDisabled}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder={t("selectDirection")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="north">{t("north")}</SelectItem>
                      <SelectItem value="south">{t("south")}</SelectItem>
                      <SelectItem value="east">{t("east")}</SelectItem>
                      <SelectItem value="west">{t("west")}</SelectItem>
                      <SelectItem value="northeast">{t("northeast")}</SelectItem>
                      <SelectItem value="northwest">{t("northwest")}</SelectItem>
                      <SelectItem value="southeast">{t("southeast")}</SelectItem>
                      <SelectItem value="southwest">{t("southwest")}</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="balconyDirection"
            render={({ field }) => (
              <FormItem className="mt-3">
                <FormLabel>{t("balconyDirection")}</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isFormDisabled}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder={t("selectBalconyDirection")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="north">{t("north")}</SelectItem>
                      <SelectItem value="south">{t("south")}</SelectItem>
                      <SelectItem value="east">{t("east")}</SelectItem>
                      <SelectItem value="west">{t("west")}</SelectItem>
                      <SelectItem value="northeast">{t("northeast")}</SelectItem>
                      <SelectItem value="northwest">{t("northwest")}</SelectItem>
                      <SelectItem value="southeast">{t("southeast")}</SelectItem>
                      <SelectItem value="southwest">{t("southwest")}</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
          <FormField
            control={form.control}
            name="width"
            className="mt-3"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("width")}</FormLabel>
                <FormControl>
                  <Input placeholder="VD: 3" {...field} type="number" suffix="m" disabled={isFormDisabled} readOnly={isFormDisabled} />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="roadWidth"
            className="mt-3"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("roadWidth")}</FormLabel>
                <FormControl>
                  <Input placeholder="VD: 3" {...field} type="number" suffix="m" disabled={isFormDisabled} readOnly={isFormDisabled} />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
          <FormField
            control={form.control}
            name="legality"
            render={({ field }) => (
              <FormItem className="mt-3">
                <FormLabel>{t("legality")}</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isFormDisabled}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder={t("selectLegality")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="so_hong">{t("pinkBook")}</SelectItem>
                      <SelectItem value="hdmb">{t("purchaseContract")}</SelectItem>
                      <SelectItem value="khac">{t("other")}</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="interior"
            render={({ field }) => (
              <FormItem className="mt-3">
                <FormLabel>{t("interior")}</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isFormDisabled}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder={t("selectInterior")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="day_du">{t("full")}</SelectItem>
                      <SelectItem value="co_ban">{t("basic")}</SelectItem>
                      <SelectItem value="khong">{t("noInterior")}</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </CollapsibleSection>
    </Card>
  );
}
