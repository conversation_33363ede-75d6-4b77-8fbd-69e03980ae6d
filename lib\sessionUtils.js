"use server";

import { cookies } from "next/headers";
import { jwtDecode } from "jwt-decode";
import { redirect } from "next/navigation";
import { jwtVerify } from "jose";

const SECRET_KEY_STRING = process.env.JWT_SECRET;

const SECRET_KEY = new TextEncoder().encode(SECRET_KEY_STRING);

export async function createSession(name, value, options) {
  const cookieStore = await cookies();

  const defaultOptions = {
    secure: true,
    httpOnly: true,
    expires: Date.now() + 24 * 60 * 60 * 1000, // 1 days
    path: "/",
    sameSite: "strict",
  };

  cookieStore.set(name, value, {
    ...defaultOptions,
    ...options,
  });
}

export async function getSession(name) {
  const cookieStore = await cookies();

  return cookieStore.get(name)?.value;
}

export async function deleteSession(name) {
  const cookieStore = await cookies();
  cookieStore.delete(name);
}

export async function fetchWithAuth(url, options = {}) {
  // 1. Lấy token từ session
  const token = await getSession("Authorization");
  if (!token) {
    console.warn("Client-side error: No token found. User is not logged in.");
    // Trả về một ApiResponse chuẩn ngay tại client
    return {
      isSuccess: false,
      data: null,
      message: "Bạn chưa đăng nhập hoặc phiên đã hết hạn.",
      errors: null,
    };
  }

  try {
    console.log(`[${options.method || "GET"}]: ${url} - Fetching with auth`);
    // 2. Gửi request với header Authorization
    const response = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        Authorization: `Bearer ${token}`,
      },
      credentials: "include",
    });

    // 3. Xử lý side-effect cho lỗi 401 (Unauthorized)
    if (response.status === 401) {
      console.warn("API returned 401. Token might be expired. Triggering logout.");
    }
    
    // 4. Xử lý trường hợp 204 No Content (trường hợp hiếm)
    if (response.status === 204) {
      return {
        isSuccess: true,
        data: null,
        message: "Thao tác thành công.",
        errors: null
      };
    }

    const apiResponse = await response.json();
    return apiResponse;

  } catch (error) {
    // 6. Xử lý lỗi mạng hoặc lỗi parse JSON
    console.error("Fetch API Error:", error);
    // Trả về một ApiResponse chuẩn cho lỗi kết nối
    return {
      isSuccess: false,
      data: null,
      message: "Không thể kết nối đến máy chủ. Vui lòng kiểm tra đường truyền và thử lại.",
      errors: null,
    };
  }
}

/**
 * Gửi request công khai (không cần xác thực) và chuẩn hóa mọi response về một cấu trúc duy nhất.
 * @param {string} url - URL của API.
 * @param {object} options - Các tùy chọn của Fetch API (method, body, etc.).
 * @returns {Promise<object>} - Luôn trả về một object có dạng { isSuccess, data, message, errors }.
 */
export async function fetchWithoutAuth(url, options = {}) {
  try {
    // 1. Gửi request (không có header Authorization)
    console.log(`[${options.method || "GET"}]: ${url} - Fetching without auth`);
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    // 2. Xử lý các trường hợp đặc biệt và chuẩn hóa response
    
    // Trường hợp 204 No Content, không có body để parse
    if (response.status === 204) {
      return { isSuccess: true, data: null, message: "Thao tác thành công.", errors: null };
    }

    const contentType = response.headers.get("content-type");
    const responseBody = await response.json();

    // Trường hợp lỗi hệ thống trả về ProblemDetails
    if (contentType && contentType.includes("application/problem+json")) {
      console.error("Middleware Error (ProblemDetails):", responseBody);
      // Chuẩn hóa ProblemDetails về cấu trúc ApiResponse
      return {
        isSuccess: false,
        data: null,
        message: responseBody.detail || responseBody.title || "Có lỗi xảy ra từ hệ thống.",
        errors: { systemError: [`Trace ID: ${responseBody.traceId || 'N/A'}`] }
      };
    }
    
    // Trường hợp còn lại, backend đã trả về ApiResponse chuẩn.
    // Chỉ cần trả về trực tiếp.
    return responseBody;

  } catch (error) {
    // 3. Xử lý lỗi mạng (kết nối thất bại, server sập,...)
    console.error("Fetch error (without auth):", error);
    // Trả về một ApiResponse chuẩn cho lỗi kết nối
    return {
      isSuccess: false,
      data: null,
      message: "Không thể kết nối đến máy chủ. Vui lòng kiểm tra đường truyền và thử lại.",
      errors: null,
    };
  }
}

export async function clearSessionAndBackToLogin() {
  const cookieStore = await cookies();
  cookieStore.delete("Authorization"); // Remove auth token
  redirect("/dang-nhap"); // Redirect to login page
}

export async function getJwtInfo() {
  const cookieStore = await cookies();
  const decodedToken = jwtDecode(cookieStore.get("Authorization")?.value);
  return decodedToken;
}

export async function verifyJwtToken(token) {
  try {
    const { payload } = await jwtVerify(token, SECRET_KEY);

    return payload;
  } catch (error) {
    // Xử lý các lỗi cụ thể do 'jose' ném ra (TokenExpiredError, JWSInvalid...)
    // Các lỗi này là runtime error do bản thân token, không phải lỗi cấu hình
    // Log các lỗi này ở mức độ thấp hơn (warn, error)
    if (error.name === 'JOSEError' && error.message === 'signature verification failed') {
         console.warn('JWT verification failed: Invalid signature.');
    } else if (error.name === 'JWTExpired') {
         console.warn('JWT verification failed: Token has expired.');
         // Bạn có thể return một giá trị đặc biệt hoặc ném lỗi khác
         // nếu middleware cần phân biệt hết hạn và invalid signature
    } else {
      console.error('Unexpected JWT verification error:', error);
    }

    return null; // Trả về null nếu token không hợp lệ hoặc hết hạn
  }
}
