# AI Agent Coding Rules for YEZHome Project

This document provides comprehensive coding rules and guidelines for AI agents working on the YEZHome real estate platform. These rules ensure consistency, maintainability, and adherence to the project's architecture.

## 🏗️ Architecture & Structure Rules

### 1. File Organization
- **MUST** place components in appropriate directories:
  - `/components/auth/` - Authentication components
  - `/components/dashboard/` - Dashboard widgets
  - `/components/property/` - Property-related components
  - `/components/ui/` - Reusable UI components (shadcn/ui)
  - `/components/user-property/` - User property management
  - `/components/wallet/` - Wallet and payment components
  - `/components/layout/` - Layout components (Navbar, Footer)

### 2. Server Actions & API Routes
- **MUST** use server actions for data mutations in `/app/actions/server/`
- **MUST** organize server actions by domain:
  - `authenticate.jsx` - Authentication operations
  - `property.jsx` - Property CRUD operations
  - `wallet.jsx` - Wallet and payment operations
  - `user.jsx` - User profile operations
- **MUST** use API routes in `/app/api/` only for external integrations

### 3. Route Structure
- **MUST** follow Next.js App Router conventions
- **MUST** use `[locale]` dynamic segment for internationalization
- **MUST** use `(protected)` route groups for authenticated pages
- **MUST** use `(auth)` route groups for authentication pages

## 🎨 Component Development Rules

### 1. Component Naming & Structure
- **MUST** use PascalCase for component names (e.g., `PropertyCard`, `SearchFilter`)
- **MUST** use `.jsx` extension for React components
- **MUST** use descriptive names indicating functionality
- **MUST** follow this component structure:
```jsx
"use client"; // Only if client component needed

import { ... } from "...";
import { useTranslations } from 'next-intl';

export default function ComponentName({ prop1, prop2 }) {
  const t = useTranslations('ComponentName');
  
  // Component logic here
  
  return (
    // JSX here
  );
}
```

### 2. UI Component Usage
- **MUST** use shadcn/ui components from `/components/ui/`
- **MUST** import UI components with proper aliases: `@/components/ui/button`
- **MUST** use Lucide React icons: `import { IconName } from "lucide-react"`
- **MUST** apply Tailwind CSS classes for styling
- **MUST** use `cn()` utility for conditional classes: `cn("base-class", condition && "conditional-class")`

### 3. Form Handling
- **MUST** use React Hook Form for complex forms
- **MUST** use Zod schemas from `/lib/schemas/` for validation
- **MUST** use server actions for form submissions
- **MUST** implement proper error handling and user feedback

## 🌐 Internationalization Rules

### 1. Translation Implementation
- **MUST** use `useTranslations` hook for client components
- **MUST** use `getTranslations` for server components
- **MUST** organize translation keys by component/feature
- **MUST** add translations to both `/messages/en.json` and `/messages/vi.json`
- **MUST** use descriptive translation keys: `"loginButton"` not `"btn1"`

### 2. Locale Handling
- **MUST** use `Link` from `@/i18n/navigation` for internal navigation
- **MUST** use `useRouter` from `@/i18n/navigation` for programmatic navigation
- **MUST** support both Vietnamese (vi) and English (en) locales
- **MUST** default to Vietnamese (vi) locale

## 🔐 Authentication & Security Rules

### 1. Authentication Flow
- **MUST** use `useAuth` hook from `@/contexts/AuthContext` for auth state
- **MUST** check authentication status before accessing protected features
- **MUST** use server-side token validation in middleware
- **MUST** handle token expiration gracefully with user feedback

### 2. Protected Routes
- **MUST** place protected pages in `(protected)` route groups
- **MUST** validate authentication server-side in layouts
- **MUST** redirect unauthenticated users to login page
- **MUST** respect user roles (Buyer/Seller) for feature access

## 💾 State Management Rules

### 1. Context Usage
- **MUST** use `AuthContext` for authentication state
- **MUST** use `AlertContext` for global alerts and confirmations
- **MUST NOT** create new contexts without architectural review
- **MUST** memoize context values to prevent unnecessary re-renders

### 2. Local State
- **MUST** use `useState` for component-level state
- **MUST** use custom hooks for reusable stateful logic
- **MUST** use `useDebounce` for search inputs and API calls
- **MUST** implement loading and error states for async operations

## 🔧 Utility & Helper Rules

### 1. Utility Functions
- **MUST** use utilities from `/lib/utils.js`:
  - `cn()` for class merging
  - `formatCurrency()` for Vietnamese currency
  - `debounce()` for performance optimization
  - `normalizeVNPhoneNumber()` for phone formatting

### 2. API Integration
- **MUST** use `fetchWithAuth()` for authenticated requests
- **MUST** use `fetchWithoutAuth()` for public requests
- **MUST** implement proper error handling with `handleErrorResponse()`
- **MUST** use server actions instead of direct API calls in components

## 🎯 Performance Rules

### 1. Component Optimization
- **MUST** use `dynamic` imports for large components
- **MUST** implement lazy loading for non-critical components
- **MUST** use `React.memo` for expensive computations
- **MUST** debounce search inputs and API calls

### 2. Image Handling
- **MUST** use Next.js `Image` component for optimization
- **MUST** implement proper image upload with UploadThing
- **MUST** use image cropping functionality when needed
- **MUST** optimize images before upload

## 🚨 Error Handling Rules

### 1. Error Management
- **MUST** implement try-catch blocks for async operations
- **MUST** display user-friendly error messages using toast notifications
- **MUST** use `AlertContext` for critical error dialogs
- **MUST** log errors for debugging: `console.error("Context:", error)`

### 2. User Feedback
- **MUST** use `toast` from `@/hooks/use-toast` for notifications
- **MUST** implement loading states for async operations
- **MUST** provide clear success/error feedback for user actions
- **MUST** use proper ARIA labels for accessibility

## 📱 Responsive Design Rules

### 1. Mobile-First Approach
- **MUST** implement mobile-first responsive design
- **MUST** use Tailwind breakpoints consistently: `sm:`, `md:`, `lg:`, `xl:`
- **MUST** test components on mobile devices
- **MUST** use `useIsMobile` hook for conditional rendering

### 2. Layout Considerations
- **MUST** ensure proper touch targets on mobile (min 44px)
- **MUST** implement responsive navigation patterns
- **MUST** optimize forms for mobile input
- **MUST** use appropriate spacing and typography scales

## 🗺️ Maps Integration Rules

### 1. Goong Maps Usage
- **MUST** use Goong Maps for all mapping functionality
- **MUST** implement proper map markers and popups
- **MUST** handle map interactions and events properly
- **MUST** optimize map performance for mobile devices

### 2. Location Handling
- **MUST** use Vietnamese address formats
- **MUST** implement proper geocoding and reverse geocoding
- **MUST** validate location data before submission
- **MUST** provide fallback for map loading failures

## 💳 Payment Integration Rules

### 1. Wallet System
- **MUST** use server actions for all payment operations
- **MUST** implement proper transaction validation
- **MUST** handle payment failures gracefully
- **MUST** provide clear transaction history

### 2. Vietnamese Payment Methods
- **MUST** support Banking, MoMo, and VietQR payment methods
- **MUST** implement proper payment method selection UI
- **MUST** validate payment amounts and methods
- **MUST** provide payment confirmation feedback

## 🧪 Testing & Quality Rules

### 1. Code Quality
- **MUST** follow TypeScript best practices
- **MUST** implement proper prop types and interfaces
- **MUST** use ESLint rules and fix all warnings
- **MUST** write self-documenting code with clear variable names

### 2. Testing Considerations
- **MUST** test components in different screen sizes
- **MUST** test authentication flows thoroughly
- **MUST** validate form submissions and error states
- **MUST** test internationalization with both locales

## 🚀 Deployment Rules

### 1. Environment Configuration
- **MUST** use environment variables for configuration
- **MUST** never commit sensitive data to repository
- **MUST** test builds before deployment
- **MUST** ensure proper SSL/HTTPS configuration

### 2. Performance Monitoring
- **MUST** monitor Core Web Vitals
- **MUST** optimize bundle sizes
- **MUST** implement proper caching strategies
- **MUST** monitor API response times

---

## ⚠️ Critical Don'ts

- **NEVER** hardcode API URLs or sensitive data
- **NEVER** bypass authentication checks
- **NEVER** ignore TypeScript errors
- **NEVER** commit console.log statements to production
- **NEVER** use inline styles instead of Tailwind classes
- **NEVER** create components without proper error boundaries
- **NEVER** ignore accessibility requirements
- **NEVER** implement features without internationalization support

---

*These rules should be followed by all AI agents working on the YEZHome project to ensure code consistency, maintainability, and adherence to project standards.*
