"use client";
import React from "react";
import ButtonLoading from "@/components/ui/ButtonLoading";
import { Eye, EyeOff, CircleAlert } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useActionState, useState } from "react";
import { registerUser } from "@/app/actions/server/authenticate";
import { useTranslations } from 'next-intl';
import { Label } from "@/components/ui/label";

const initialState = {
  errors: {},
  message: null,
  fieldValues: {
    username: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: "",
    userType: "",
  },
};

export default function RegisterForm() {
  const t = useTranslations('RegisterPage');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [state, formAction, isPending] = useActionState(registerUser, initialState);

  return (
    <form className="mt-8 space-y-6" action={formAction}>
      {state.message && (
        <Alert variant="destructive" className="bg-red-600 text-white border-red-800">
          <CircleAlert className="h-4 w-4 text-white" />
          <AlertTitle className="text-white">{t("registerErrorTitle")}</AlertTitle>
          <AlertDescription className="text-white">{state.message}</AlertDescription>
        </Alert>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column - Email and Password */}
        <div className="space-y-4">
          {/* Email */}
          <div className="space-y-1">
            <Label htmlFor="email" className="text-sm font-medium text-charcoal">
              {t("emailLabel")}
            </Label>
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              defaultValue={state.fieldValues?.email}
              className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-charcoal focus:outline-hidden focus:ring-teal-500 focus:border-teal-500 focus:z-10 sm:text-sm"
              placeholder={t("emailPlaceholder")}
            />
            {state.errors?.email && (
              <p className="text-xs text-red-500">{state.errors.email[0]}</p>
            )}
          </div>
          
          {/* Password */}
          <div className="space-y-1">
            <Label htmlFor="password" className="text-sm font-medium text-charcoal">
              {t("passwordLabel")}
            </Label>
            <div className="relative">
              <input
                id="password"
                name="password"
                type={showPassword ? "text" : "password"}
                required
                defaultValue={state.fieldValues?.password}
                className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-charcoal focus:outline-hidden focus:ring-teal-500 focus:border-teal-500 focus:z-10 sm:text-sm"
                placeholder={t("passwordPlaceholder")}
                minLength={6}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5 text-gray-400" />
                ) : (
                  <Eye className="h-5 w-5 text-gray-400" />
                )}
              </button>
            </div>
            {state.errors?.password && (
              <p className="text-xs text-red-500">{state.errors.password[0]}</p>
            )}
          </div>
          
          {/* Confirm Password */}
          <div className="space-y-1">
            <Label htmlFor="confirmPassword" className="text-sm font-medium text-charcoal">
              {t("confirmPasswordLabel")}
            </Label>
            <div className="relative">
              <input
                id="confirmPassword"
                name="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                required
                defaultValue={state.fieldValues?.confirmPassword}
                className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-charcoal focus:outline-hidden focus:ring-teal-500 focus:border-teal-500 focus:z-10 sm:text-sm"
                placeholder={t("confirmPasswordPlaceholder")}
                minLength={6}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-5 w-5 text-gray-400" />
                ) : (
                  <Eye className="h-5 w-5 text-gray-400" />
                )}
              </button>
            </div>
            {state.errors?.confirmPassword && (
              <p className="text-xs text-red-500">{state.errors.confirmPassword[0]}</p>
            )}
          </div>
        </div>
        
        {/* Right Column - Personal Info */}
        <div className="space-y-4">
          {/* Full Name */}
          <div className="space-y-1">
            <Label htmlFor="fullname" className="text-sm font-medium text-charcoal">
              {t("fullNameLabel")}
            </Label>
            <input
              id="fullname"
              name="fullname"
              type="text"
              required
              defaultValue={state.fieldValues?.fullname}
              className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-charcoal rounded-md focus:outline-hidden focus:ring-teal-500 focus:border-teal-500 focus:z-10 sm:text-sm"
              placeholder={t("usernamePlaceholder")}
            />
            {state.errors?.username && (
              <p className="text-xs text-red-500">{state.errors.username[0]}</p>
            )}
          </div>
          
          {/* Phone */}
          <div className="space-y-1">
            <Label htmlFor="phone" className="text-sm font-medium text-charcoal">
              {t("phoneLabel")}
            </Label>
            <input
              id="phone"
              name="phone"
              type="tel"
              autoComplete="tel"
              required
              defaultValue={state.fieldValues?.phone}
              className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-charcoal focus:outline-hidden focus:ring-teal-500 focus:border-teal-500 focus:z-10 sm:text-sm"
              placeholder={t("phonePlaceholder")}
            />
            {state.errors?.phone && (
              <p className="text-xs text-red-500">{state.errors.phone[0]}</p>
            )}
          </div>
          
          {/* User Type */}
          <div className="space-y-1">
            <Label className="text-sm font-medium text-charcoal">
              {t("userTypeQuestion")}
            </Label>
            <div className="flex items-center space-x-4 pt-2">
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  name="userType"
                  value="Buyer"
                  defaultChecked={state.fieldValues?.userType === "Buyer"}
                  className="form-radio h-4 w-4 text-coral-500 focus:ring-teal-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-charcoal">{t("userTypeBuyer")}</span>
              </label>
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  name="userType"
                  value="Seller"
                  defaultChecked={state.fieldValues?.userType === "Seller"}
                  className="form-radio h-4 w-4 text-coral-500 focus:ring-teal-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-charcoal">{t("userTypeSeller")}</span>
              </label>
            </div>
            {state.errors?.userType && (
              <p className="text-xs text-red-500">{state.errors.userType[0]}</p>
            )}
          </div>
        </div>
      </div>
      <div className="text-sm text-charcoal border-t border-gray-300 pt-4">
        <span className="font-bold">Email (*):</span> để nhận thông báo từ người dùng và YEZ Home
        <br />
        <span className="font-bold">Số điện thoại (*):</span> để YEZ Home xác thực người dùng và để người mua liên hệ với bạn
      </div>
      <div className="pt-4">
        <ButtonLoading type="submit" showLoading={isPending} title={t("registerButton")} />
      </div>
    </form>
  );
}
