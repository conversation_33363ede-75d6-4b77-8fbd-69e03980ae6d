{"name": "yezhome", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --experimental-https --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@goongmaps/goong-js": "^1.0.9", "@hookform/resolvers": "^3.10.0", "@uploadthing/react": "^7.2.1", "bowser": "^2.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "framer-motion": "^12.9.4", "jose": "^6.0.11", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.12.10", "lucide-react": "^0.473.0", "next": "15.5.0", "next-intl": "^4.0.2", "next-share": "^0.27.0", "radix-ui": "latest", "react": "19.1.1", "react-dom": "19.1.1", "react-dropzone": "^14.3.5", "react-easy-crop": "^5.5.1", "react-hook-form": "^7.54.2", "react-multi-carousel": "^2.8.6", "react-number-format": "^5.4.3", "react-photo-album": "^3.1.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.5.1", "use-debounce": "^10.0.4", "uuid": "^11.1.0", "yet-another-react-lightbox": "^3.22.0", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@types/node": "22.13.4", "@types/react": "19.1.11", "eslint": "^9", "eslint-config-next": "15.5.0", "postcss": "^8", "tailwindcss": "^4.1.11", "typescript": "5.8.2"}, "overrides": {"@types/react": "19.1.11"}}