import { Mail, MapPin, Phone, Send } from "lucide-react";
import Image from "next/image";
import { Link } from "@/i18n/navigation";

export default function Footer() {
  return (
    <footer className="bg-slate-900 text-slate-200 py-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
        {/* 1. Logo + Địa chỉ */}
        <div>
          <Image src="/logo.png" alt="YEZHOME logo" width={120} height={40} priority className="mb-4" />
          <p className="text-sm font-semibold text-white">YEZHOME VIỆT NAM</p>
          <p className="text-sm mt-1 flex items-start gap-2">
            <MapPin size={16} /> Tầng 100, Landmark 100, Hồ Chí Minh City
          </p>
          <p className="text-sm mt-1">📞 (084) 1234 1234 - (084) 2345 2345</p>
          <div className="flex items-center gap-3 mt-3">
            <img src="/google-play.png" alt="Google Play" className="h-8" />
            <img src="/qrcode.png" alt="QR Code" className="h-16" />
          </div>
        </div>

        {/* 2. Hướng dẫn */}
        <div>
          <h4 className="text-white font-semibold mb-3">HƯỚNG DẪN</h4>
          <ul className="space-y-3 text-sm text-slate-300">
            <li>
              <Link href="/about" className="hover:text-teal-500">
                Về chúng tôi
              </Link>
            </li>
            <li>
              <Link href="/pricing" className="hover:text-teal-500">
                Báo giá và hỗ trợ
              </Link>
            </li>
            <li>
              <Link href="/faq" className="hover:text-teal-500">
                Câu hỏi thường gặp
              </Link>
            </li>
            <li>
              <Link href="/feedback" className="hover:text-teal-500">
                Góp ý báo lỗi
              </Link>
            </li>
            <li>
              <Link href="/sitemap" className="hover:text-teal-500">
                Sitemap
              </Link>
            </li>
          </ul>
        </div>

        {/* 3. Quy định */}
        <div>
          <h4 className="text-white font-semibold mb-3">QUY ĐỊNH</h4>
          <ul className="space-y-3 text-sm text-slate-300">
            <li>
              <Link href="#" className="hover:text-teal-500">
                Quy định đăng tin
              </Link>
            </li>
            <li>
              <Link href="#" className="hover:text-teal-500">
                Quy chế hoạt động
              </Link>
            </li>
            <li>
              <Link href="#" className="hover:text-teal-500">
                Điều khoản thỏa thuận
              </Link>
            </li>
            <li>
              <Link href="#" className="hover:text-teal-500">
                Chính sách bảo mật
              </Link>
            </li>
            <li>
              <Link href="#" className="hover:text-teal-500">
                Giải quyết khiếu nại
              </Link>
            </li>
          </ul>
        </div>

        {/* 4. Liên hệ + Đăng ký nhận tin */}
        <div>
          <h4 className="text-white font-semibold mb-3">LIÊN HỆ</h4>
          <p className="text-sm flex items-center gap-2">
            <Phone size={16} /> Hotline: 1900 1234
          </p>
          <p className="text-sm flex items-center gap-2 mt-2">
            <Mail size={16} /> <EMAIL>
          </p>
          <p className="text-sm flex items-center gap-2 mt-1">
            <Mail size={16} /> <EMAIL>
          </p>

          <h4 className="text-white font-semibold mt-5 mb-2">ĐĂNG KÝ NHẬN TIN</h4>
          <form className="flex mt-2">
            <input
              type="email"
              placeholder="Nhập email của bạn"
              className="w-full px-4 py-2 text-sm rounded-l-lg bg-slate-800 border border-slate-700 text-white placeholder-slate-400 focus:outline-hidden"
            />
            <button type="submit" className="bg-teal-600 px-4 rounded-r-lg hover:bg-teal-700 transition">
              <Send size={18} color="white" />
            </button>
          </form>
        </div>
      </div>

      {/* Footer bottom line */}
      <div className="text-center text-sm text-slate-500 mt-10 px-4">© 2025 YEZ HOME. All rights reserved.</div>
    </footer>
  );
}
