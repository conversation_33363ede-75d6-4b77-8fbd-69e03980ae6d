"use client";

import { useState, useMemo } from "react";
import Image from "next/image";
import { Grid3X3, X } from "lucide-react";
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";

// Import lightbox plugins
import Fullscreen from "yet-another-react-lightbox/plugins/fullscreen";
import Slideshow from "yet-another-react-lightbox/plugins/slideshow";
import Zoom from "yet-another-react-lightbox/plugins/zoom";

// Import Dialog components
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "../ui/scroll-area";
import { Button } from "../ui/button";
import CustomPhotoGallery from "./CustomPhotoGallery";

export default function PropertyImageGallery({ images = [], propertyName = "" }) {
  const [isGalleryOpen, setIsGalleryOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(-1);


  // Prepare slides for the lightbox
  const slides = useMemo(() => {
    return images.map((src) => ({
      src,
      alt: propertyName || "Property image",
    }));
  }, [images, propertyName]);

  const totalImages = images.length; // Total number of images available
  const remainingImages = Math.max(0, totalImages - 5);

  const renderGallery = () => {
    if (images.length === 0) {
      return (
        <div className="flex items-center justify-center h-[500px] bg-gray-200 rounded-lg">
          <p className="text-gray-500">Không có ảnh</p>
        </div>
      );
    }

    if (images.length === 1) {
      return (
        <div className="relative overflow-hidden rounded-lg h-[500px] cursor-pointer" onClick={() => setIsGalleryOpen(true)}>
          <Image
            src={images[0] || "/placeholder.svg"}
            alt={propertyName || "Property image"}
            fill
            className="object-cover hover:scale-105 transition-transform duration-300"
            priority
          />
        </div>
      );
    }

    if (images.length === 2) {
      return (
        <div className="grid grid-cols-2 gap-4 h-[500px]">
          {images.map((src, index) => (
            <div key={index} className="relative overflow-hidden rounded-lg cursor-pointer" onClick={() => setIsGalleryOpen(true)}>
              <Image
                src={src || "/placeholder.svg"}
                alt={`${propertyName || "Property"} image ${index + 1}`}
                fill
                className="object-cover hover:scale-105 transition-transform duration-300"
                priority={index === 0}
              />
            </div>
          ))}
        </div>
      );
    }

    if (images.length === 3) {
      return (
        <div className="flex gap-4 h-[500px]">
          <div className="flex-1 relative overflow-hidden rounded-lg cursor-pointer" onClick={() => setIsGalleryOpen(true)}>
            <Image
              src={images[0] || "/placeholder.svg"}
              alt={propertyName || "Property image"}
              fill
              className="object-cover hover:scale-105 transition-transform duration-300"
              priority
            />
          </div>
          <div className="w-96 flex flex-col gap-4">
            {images.slice(1).map((src, index) => (
              <div key={index + 1} className="flex-1 relative overflow-hidden rounded-lg cursor-pointer" onClick={() => setIsGalleryOpen(true)}>
                <Image
                  src={src || "/placeholder.svg"}
                  alt={`${propertyName || "Property"} image ${index + 2}`}
                  fill
                  className="object-cover hover:scale-105 transition-transform duration-300"
                />
              </div>
            ))}
          </div>
        </div>
      );
    }

    if (images.length === 4) {
      return (
        <div className="flex gap-4 h-[500px]">
          <div className="flex-1 relative overflow-hidden rounded-lg cursor-pointer" onClick={() => setIsGalleryOpen(true)}>
            <Image
              src={images[0] || "/placeholder.svg"}
              alt={propertyName || "Property image"}
              fill
              className="object-cover hover:scale-105 transition-transform duration-300"
              priority
            />
          </div>
          <div className="w-96 grid grid-cols-1 gap-4">
            {images.slice(1).map((src, index) => (
              <div key={index + 1} className="relative overflow-hidden rounded-lg cursor-pointer" onClick={() => setIsGalleryOpen(true)}>
                <Image
                  src={src || "/placeholder.svg"}
                  alt={`${propertyName || "Property"} image ${index + 2}`}
                  fill
                  className="object-cover hover:scale-105 transition-transform duration-300"
                />
              </div>
            ))}
          </div>
        </div>
      );
    }

    // 5 or more images - original layout
    return (
      <div className="flex gap-4 h-[500px]">
        <div className="flex-1 relative overflow-hidden rounded-lg cursor-pointer" onClick={() => setIsGalleryOpen(true)}>
          <Image
            src={images[0] || "/placeholder.svg"}
            alt={propertyName || "Property image"}
            fill
            className="object-cover hover:scale-105 transition-transform duration-300"
            priority
          />
        </div>
        <div className="flex-1">
          <div className="h-full grid grid-cols-2 gap-4">
            {images.slice(1, 5).map((src, index) => (
              <div
                key={index + 1}
                className={`relative overflow-hidden rounded-lg cursor-pointer ${index === 3 && remainingImages > 0 ? "group" : ""}`}
                onClick={() => setIsGalleryOpen(true)}
              >
                <Image
                  src={src || "/placeholder.svg"}
                  alt={`${propertyName || "Property"} image ${index + 2}`}
                  fill
                  className="object-cover hover:scale-105 transition-transform duration-300"
                />
                {index === 3 && remainingImages > 0 && (
                  <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Button
                      variant="secondary"
                      size="sm"
                      className="gap-2 bg-white/90 text-gray-800 hover:bg-white"
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsGalleryOpen(true);
                      }}
                    >
                      <Grid3X3 className="w-4 h-4" />
                      Xem tất cả {totalImages} ảnh
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {renderGallery()}

      {/* Gallery Modal */}
      <Dialog open={isGalleryOpen} onOpenChange={setIsGalleryOpen}>
        <DialogContent className="max-w-7xl p-6 h-[94vh] w-[90vw] bg-white [&>button:last-child]:hidden">
          <DialogTitle className="sr-only">{propertyName ? `${propertyName} Images` : "Property Images"}</DialogTitle>
          
          {/* Custom Close Button */}
          <div className="absolute top-4 right-4 z-50">
            <Button
              variant="outline"
              size="sm"
              className="gap-2 bg-white/90 hover:bg-white border-gray-300 text-gray-700"
              onClick={() => setIsGalleryOpen(false)}
            >
              <X className="w-4 h-4" />
              Đóng
            </Button>
          </div>

          <div className="relative h-full w-full flex flex-col pt-5">
            <ScrollArea className="h-[90vh]">
              <div className="p-4">
                <CustomPhotoGallery 
                  images={images}
                  propertyName={propertyName}
                  onImageClick={setLightboxIndex}
                />
              </div>
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>

      {/* Lightbox for individual image viewing */}
      <Lightbox
        slides={slides}
        open={lightboxIndex >= 0}
        index={lightboxIndex}
        close={() => setLightboxIndex(-1)}
        plugins={[Fullscreen, Slideshow, Zoom]}
      />
    </>
  );
}
