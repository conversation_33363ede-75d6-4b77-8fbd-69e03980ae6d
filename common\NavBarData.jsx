import { Chart<PERSON>ie, Clock10Icon, Layers, MessageSquareDot, Settings, User } from "lucide-react";

export const NavBarData = [
  {
    id: "home",
    name: "Trang chủ",
    url: "/",
    subnav: [],
  },
  {
    id: "post",
    name: "<PERSON> tứ<PERSON>",
    url: "/tin-tuc",
    subnav: [],
  },
  {
    id: "about",
    name: "<PERSON><PERSON><PERSON><PERSON> thiệu",
    url: "/gioi-thieu",
    subnav: [],
  },
  {
    id: "contact",
    name: "<PERSON><PERSON><PERSON> hệ",
    url: "/lien-he",
    subnav: [],
  },
];

export const sidebarMenuItems = [
  { href: "/user/dashboard", label: "Tổng quan", icon: ChartPie },
  { href: "/user/profile", label: "Thông tin cá nhân", icon: User },
  { href: "/user/bds", label: "Quản lý tin đăng", icon: Layers },
  { href: "/user/notifications", label: "Thông báo", icon: MessageSquareDot },
  { href: "/user/transactions", label: "L<PERSON>ch sử giao dịch", icon: Clock10Icon },
  { href: "/user/setting", label: "Tiện ích", icon: Settings },
];
