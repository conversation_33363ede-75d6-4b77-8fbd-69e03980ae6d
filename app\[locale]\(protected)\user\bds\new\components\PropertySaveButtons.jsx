"use client";

import React, { useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, TriangleAlert } from "lucide-react";
import { Link } from "@/i18n/navigation";
import { FormType, CAN_NOT_EDIT_STATUS } from "@/lib/enum";
import { useTranslations } from "next-intl";

// Memoize the component to prevent unnecessary re-renders
const PropertySaveButtons = React.memo(
  ({ formType, propertyStatus, isPending, formHandleSubmit, onSubmit }) => {
    const t = useTranslations("PropertyForm");
    const tCommon = useTranslations("Common");

    // Get the translated property status
    const getTranslatedStatus = useCallback(
      (status) => {
        const statusKey = `propertyStatus_${status}`;
        return tCommon(statusKey);
      },
      [tCommon]
    );

    // Memoize the handler for saving as draft
    const handleSaveDraft = useCallback(() => {
      return formHandleSubmit((data) => onSubmit(data, "saveDraft"))();
    }, [formHandleSubmit, onSubmit]);

    if (formType === FormType.EDIT && CAN_NOT_EDIT_STATUS.includes(propertyStatus)) {
      return (
        <div className="flex flex-col items-center justify-center">
          <Alert className="bg-orange-50 border-orange-200 text-orange-900 mb-4">
            <TriangleAlert className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              <p className="mb-4">
                {t("propertyIsBeingReviewedOrApprovedOrSold", {
                  propertyStatus: getTranslatedStatus(propertyStatus),
                })}
              </p>
              <Link
                href="/user/bds"
                className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-charcoal transition-colors"
              >
                {t("backToPropertyList")}
              </Link>
            </AlertDescription>
          </Alert>
        </div>
      );
    } else {
      return (
        <div className="flex gap-4 items-center justify-center">
          <Button type="button" className="py-4 bg-gray-500 hover:bg-gray-600" onClick={handleSaveDraft} disabled={isPending}>
            {isPending ? t("processing") : t("saveDraft")}
          </Button>

          <Button type="submit" className="py-4 bg-teal-600 hover:bg-teal-700" disabled={isPending}>
            {isPending ? t("processing") : t("saveAndSendForApproval")}
          </Button>
        </div>
      );
    }
  },
  (prevProps, nextProps) => {
    // Only re-render if these props change
    return (
      prevProps.formType === nextProps.formType &&
      prevProps.propertyStatus === nextProps.propertyStatus &&
      prevProps.isPending === nextProps.isPending
      // We don't compare formHandleSubmit and onSubmit as they should be memoized by the parent
    );
  }
);

export default PropertySaveButtons;
