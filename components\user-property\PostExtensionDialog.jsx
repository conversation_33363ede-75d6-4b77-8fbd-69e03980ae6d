"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, Calendar, Wallet } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useTranslations } from "next-intl";
import { useAuth } from "@/contexts/AuthContext";
import { formatCurrency } from "@/lib/utils";
import { EXTENSION_PACKAGES } from "@/lib/enum";

export default function PostExtensionDialog({ postId, postTitle, open, onClose, onConfirm }) {
  const t = useTranslations("PropertyCard");
  const tCommon = useTranslations("Common");
  const { profile } = useAuth();
  const walletInfo = profile?.user?.wallet;
  const [selectedPackage, setSelectedPackage] = useState(null); 
  const [isProcessing, setIsProcessing] = useState(false);

  const handleExtend = async () => {
    if (!selectedPackage) return;

    if (walletInfo.balance < selectedPackage.price) {
      return;
    }

    setIsProcessing(true);
    try {
      await onConfirm(selectedPackage.days, selectedPackage.price);
      setSelectedPackage(null);
    } catch (error) {
      console.error("Extension failed:", error);
    } finally {
      setIsProcessing(false);
      onClose();
    }
  };

  const isInsufficientBalance = selectedPackage && walletInfo && walletInfo.balance < selectedPackage.price;
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Gia hạn bài viết
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Post Info */}
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm text-muted-foreground">Bài viết</p>
            <p className="font-medium truncate">{postTitle}</p>
          </div>

          {/* Current Balance */}
          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2">
              <Wallet className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium">Số dư hiện tại</span>
            </div>
            <span className="font-bold text-green-700">{walletInfo && formatCurrency(walletInfo.balance)}</span>
          </div>

          {/* Extension Packages */}
          <div className="space-y-3">
            <h3 className="font-medium">Chọn gói gia hạn</h3>
            <div className="grid grid-cols-2 gap-3">
            {EXTENSION_PACKAGES.map((pkg) => (
                <Card
                  key={pkg.days}
                  className={`cursor-pointer transition-all hover:shadow-sm ${
                    selectedPackage?.days === pkg.days
                      ? "ring-2 ring-primary border-primary"
                      : "hover:border-primary/50"
                  }`}
                  onClick={() => setSelectedPackage(pkg)}
                >
                  <CardContent className="p-3 text-center relative">
                    {pkg.popular && (
                      <Badge className="absolute -top-1 left-1/2 transform -translate-x-1/2 bg-orange-500 text-xs px-1 py-0">
                        Phổ biến
                      </Badge>
                    )}
                    <div className="space-y-1">
                      <div className="text-lg font-bold text-primary">{pkg.days}</div>
                      <div className="text-xs text-muted-foreground">ngày</div>
                      <div className="font-medium text-xs">{formatCurrency(pkg.price)}</div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Insufficient Balance Warning */}
          {isInsufficientBalance && (
            <Alert variant="destructive" className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>👉 Số dư không đủ để gia hạn. Vui lòng nạp thêm tiền.</AlertDescription>
            </Alert>
          )}

          {/* Selected Package Summary */}
          {selectedPackage && !isInsufficientBalance && (
            <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex justify-between items-center">
                <span className="text-sm">Gói đã chọn:</span>
                <span className="font-medium">{selectedPackage.days} ngày</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Tổng tiền:</span>
                <span className="font-bold text-blue-700">{formatCurrency(selectedPackage.price)}</span>
              </div>
              <div className="flex justify-between items-center text-sm text-muted-foreground">
                <span>Số dư sau gia hạn:</span>
                <span>{walletInfo && formatCurrency(walletInfo.balance - selectedPackage.price)}</span>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            <Button variant="outline" className="flex-1 bg-transparent" onClick={onClose}>
              {t("cancel")}
            </Button>
            <Button className="flex-1" onClick={handleExtend} disabled={!selectedPackage || isInsufficientBalance || isProcessing}>
              {isProcessing ? tCommon("processing") : t("confirm")}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
