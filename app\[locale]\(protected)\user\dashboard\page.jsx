"use client";

import WalletInfo from "@/components/dashboard/WalletInfo";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Wallet, Home, RefreshCw } from "lucide-react";
import CollapseHeader from "@/components/ui/collapse";
import RecentTransactions from "@/components/dashboard/RecentTransactions";
import PropertyStats from "@/components/dashboard/PropertyStats";
import MemberRanking from "@/components/dashboard/MemberRanking";
import { Button } from "@/components/ui/button";
import { Link } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import { useAuth } from "@/contexts/AuthContext";
import { getUserDashboard } from "@/app/actions/server/user";
import { useState, useEffect, useCallback } from "react";

export default function Dashboard() {
  const t = useTranslations('UserDashboardPage');
  const { profile } = useAuth();
  const profileWalletInfo = profile?.user?.wallet;

  // Local state for dashboard data
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Function to fetch user dashboard data
  const fetchUserDashboard = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getUserDashboard();
      if (response?.isSuccess) {
        setDashboardData(response.data);
        setError(null);
      } else {
        setError(response.message);
      }
    } catch (err) {
      setError("Failed to fetch user dashboard");
      console.error("Error fetching user dashboard:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch dashboard data when component mounts
  useEffect(() => {
    fetchUserDashboard();
  }, [fetchUserDashboard]);

  // Function to manually refresh dashboard data
  const refreshUserData = useCallback(() => {
    fetchUserDashboard();
  }, [fetchUserDashboard]);

  if (loading) {
    return (
      <div className="p-6">
        <div className="h-96 flex items-center justify-center">
          <div className="text-coral-500">{t('loadingMessage')}</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="h-96 flex items-center justify-center">
          <div className="text-red-500">{error}</div>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="p-6">
        <div className="h-96 flex items-center justify-center">
          <div className="text-gray-500">{t('noData')}</div>
        </div>
      </div>
    );
  }

  const { userInfo, propertyStats, recentTransactions, memberRanking } = dashboardData;

  const walletInfo = profileWalletInfo || (dashboardData && dashboardData.walletInfo);

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-navy-blue">{t('greeting', {fullName: userInfo.fullName})}</h1>
          <p className="text-gray-500">
            {t('lastLoginLabel')} {new Date(userInfo.lastLogin).toLocaleString("vi-VN")}
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex gap-2">
          <Button
            size="icon"
            variant="ghost"
            onClick={() => refreshUserData()}
            title="Refresh dashboard data"
            className="mr-2"
            aria-label="Refresh dashboard data"
          >
            <RefreshCw className="h-5 w-5" />
          </Button>
          <Button asChild>
            <Link href="/user/properties/create">
              {t('postListingButton')}
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/user/wallet">
              {t('depositButton')}
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-6">
        <Card className="col-span-1 md:col-span-2 lg:col-span-4">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium flex items-center">
              <Wallet strokeWidth={1.5} className="mr-2 h-5 w-5" />
              {t('walletCardTitle')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <WalletInfo walletInfo={walletInfo} />
          </CardContent>
        </Card>

        <Card className="col-span-1 md:col-span-2 lg:col-span-8">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium flex items-center">
              <Home className="mr-2 h-5 w-5" />
              {t('propertyStatsCardTitle')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <PropertyStats propertyStats={propertyStats} />
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
        <div className="col-span-1 md:col-span-8">
          <CollapseHeader title={t('recentTransactionsTitle')}>
            <RecentTransactions transactions={recentTransactions} />
          </CollapseHeader>
        </div>

        <div className="col-span-1 md:col-span-4">
          <CollapseHeader title={t('memberRankingTitle')}>
            <MemberRanking memberRanking={memberRanking} />
          </CollapseHeader>
        </div>
      </div>
    </div>
  );
}