"use client";

import { Send, User, Phone, Mail, Star } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { useTranslations } from "next-intl";

export default function PropertyContactSection({ 
  property, 
  onContactClick,
  className = "" 
}) {
  const tCommon = useTranslations("Common");
  
  const owner = property?.owner;
  
  // Get member rank display
  const getMemberRankDisplay = (rank) => {
    switch (rank) {
      case 'bronze':
        return { text: tCommon('bronze'), color: 'bg-amber-600' };
      case 'silver':
        return { text: tCommon('silver'), color: 'bg-gray-400' };
      case 'gold':
        return { text: tCommon('gold'), color: 'bg-yellow-500' };
      case 'diamond':
        return { text: tCommon('diamond'), color: 'bg-blue-600' };
      case 'platinum':
        return { text: tCommon('platinum'), color: 'bg-purple-600' };
      default:
        return { text: tC<PERSON><PERSON>('default'), color: 'bg-gray-500' };
    }
  };

  const memberRankInfo = getMemberRankDisplay(owner?.memberRank);

  return (
    <div className={`bg-white border rounded-md p-4 sticky top-20 ${className}`}>
      {/* Owner Information */}
      {owner && (
        <div className="mb-4 pb-4 border-b">
          <div className="flex items-center gap-3 mb-3">
            <Avatar className="h-12 w-12">
              <AvatarImage 
                src={owner.avatarURL} 
                alt={owner.fullName || "Chủ sở hữu"} 
              />
              <AvatarFallback className="bg-gray-100">
                <User className="h-6 w-6 text-gray-500" />
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 text-base">
                {owner.fullName || "Chủ sở hữu"}
              </h3>
              <Badge 
                variant="secondary" 
                className={`text-xs text-white ${memberRankInfo.color}`}
              >
                <Star className="h-3 w-3 mr-1" />
                {memberRankInfo.text}
              </Badge>
            </div>
          </div>
          
          {/* Contact Information */}
          <div className="space-y-2 text-sm">
            {owner.phone && (
              <div className="flex items-center gap-2 text-gray-600">
                <Phone className="h-4 w-4" />
                <span>{owner.phone}</span>
              </div>
            )}
            {owner.email && (
              <div className="flex items-center gap-2 text-gray-600">
                <Mail className="h-4 w-4" />
                <span className="truncate">{owner.email}</span>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Contact Button */}
      <button
        className="w-full bg-teal-600 text-white hover:bg-teal-700 font-semibold py-3 px-3 rounded-md text-lg flex items-center justify-center gap-2 transition-colors"
        onClick={onContactClick}
        type="button"
      >
        <Send className="h-5 w-5" /> 
        Liên hệ người bán
      </button>
    </div>
  );
}
