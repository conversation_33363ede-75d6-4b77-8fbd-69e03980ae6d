import * as React from "react"
import { cva } from "class-variance-authority"
import { Info, AlertTriangle, XCircle } from "lucide-react"

import { cn } from "@/lib/utils"

const alertVariants = cva(
  "relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",
  {
    variants: {
      variant: {
        default: "bg-card text-card-foreground",
        destructive:
          "text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90",
        info: "bg-blue-50 border-blue-200 text-blue-800 [&>svg]:text-blue-600 dark:bg-blue-950/30 dark:border-blue-800 dark:text-blue-200 dark:[&>svg]:text-blue-400",
        warning:
          "bg-yellow-50 border-yellow-200 text-yellow-800 [&>svg]:text-yellow-600 dark:bg-yellow-950/30 dark:border-yellow-800 dark:text-yellow-200 dark:[&>svg]:text-yellow-400",
        error:
          "bg-red-50 border-red-200 text-red-800 [&>svg]:text-red-600 dark:bg-red-950/30 dark:border-red-800 dark:text-red-200 dark:[&>svg]:text-red-400",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
)

const variantIcons = {
  info: Info,
  warning: AlertTriangle,
  error: XCircle,
  destructive: XCircle,
}

function Alert({
  className,
  variant,
  children,
  ...props
}) {
  const IconComponent = variant && variant in variantIcons ? variantIcons[variant] : null
  const hasIcon = React.Children.toArray(children).some((child) => React.isValidElement(child) && child.type === "svg")

  return (
    <div data-slot="alert" role="alert" className={cn(alertVariants({ variant }), className)} {...props}>
      {IconComponent && !hasIcon && <IconComponent />}
      {children}
    </div>
  )
}

function AlertTitle({ className, ...props }) {
  return (
    <div
      data-slot="alert-title"
      className={cn("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight", className)}
      {...props}
    />
  )
}

function AlertDescription({ className, ...props }) {
  return (
    <div
      data-slot="alert-description"
      className={cn(
        "text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",
        className,
      )}
      {...props}
    />
  )
}

export { Alert, AlertTitle, AlertDescription }
