"use server";

import { fetchWithoutAuth } from "@/lib/sessionUtils";

const API_BASE_URL = `${process.env.API_URL}/api/YezData`;

/**
 * Get listing prices for property extensions
 * @returns {Promise<Object>} Response with listing prices data
 */
export async function getListingPrices() {
  const url = `${API_BASE_URL}/listing-prices`;
  
  const response = await fetchWithoutAuth(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  return response;
}
