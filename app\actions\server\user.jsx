"use server";

import { fetchWithAuth } from "@/lib/sessionUtils";

const API_USER_BASE_URL = `${process.env.API_URL}/api/user`;

export async function getUserDashboard() {
  return await fetchWithAuth(`${API_USER_BASE_URL}/dashboard`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}



export async function getUserPropertyStats() {
  return await fetchWithAuth(`${API_USER_BASE_URL}/properties/stats`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}



export async function getUserRanking() {
  return await fetchWithAuth(`${API_USER_BASE_URL}/ranking`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export async function getMonthlySpending(year) {
  return await fetchWithAuth(`${API_USER_BASE_URL}/spending/monthly?year=${year}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export async function getPropertyPerformance() {
  return await fetchWithAuth(`${API_USER_BASE_URL}/properties/performance`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}



export async function verifyUserRank() {
  const response = await fetchWithAuth(`${API_USER_BASE_URL}/verify-user-rank`, {
    method: "GET",
  });
  return response;
}

// Get user tax information
export async function getUserTaxInfo() {
  return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

// Update user tax information
export async function updateUserTaxInfo(taxInfo) {
  return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(taxInfo),
  });
}

// Deactivate user account
export async function deactivateUserAccount(data) {
  return await fetchWithAuth(`${API_USER_BASE_URL}/deactivate`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
}

// Request permanent account deletion
export async function requestAccountDeletion(data) {
  return await fetchWithAuth(`${API_USER_BASE_URL}/permanent-delete`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
}

export async function uploadAvatar(file) {
  const formData = new FormData();
  formData.append('file', file);

  return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar/upload`, {
    method: "POST",
    body: formData,
  });
}

export async function deleteAvatar() {
  return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar`, {
    method: "DELETE",
  });
}
