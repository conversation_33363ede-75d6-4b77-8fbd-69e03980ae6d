import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getPropertyBySlug } from "@/app/actions/server/property";
import PropertyDetailWrapper from "./PropertyDetailWrapper";

interface PropertyDetailPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateMetadata({ params }: PropertyDetailPageProps): Promise<Metadata> {
  const property = await getPropertyBySlug((await params).slug);

  if (!property || !property.isSuccess) {
    return {
      title: "Property Not Found | YEZ Home",
      description: "The requested property could not be found.",
    };
  }

  return {
    title: `${property.data.name} | YEZ Home`,
    description: property.data.description || `View details about ${property.data.name} on YEZ Home`,
  };
}

// This is a server component that fetches the data and passes it to the client component
export default async function PropertyDetailPage({ params }: PropertyDetailPageProps) {
  const property = await getPropertyBySlug((await params).slug);
  if (!property || property.isSuccess === false) {
    notFound();
  }

  return <PropertyDetailWrapper property={property.data} />;
}
