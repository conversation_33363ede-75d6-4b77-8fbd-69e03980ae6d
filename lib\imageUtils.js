/**
 * Creates a cropped canvas from an image using crop data
 * @param {HTMLImageElement} imageSrc - The source image
 * @param {Object} crop - The crop data from react-easy-crop
 * @returns {HTMLCanvasElement} - The cropped canvas
 */
export const createCroppedCanvas = (imageSrc, crop) => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('No 2d context');
  }

  const scaleX = imageSrc.naturalWidth / imageSrc.width;
  const scaleY = imageSrc.naturalHeight / imageSrc.height;
  
  // Set canvas size to crop size
  canvas.width = crop.width;
  canvas.height = crop.height;

  // Draw the cropped image
  ctx.drawImage(
    imageSrc,
    crop.x * scaleX,
    crop.y * scaleY,
    crop.width * scaleX,
    crop.height * scaleY,
    0,
    0,
    crop.width,
    crop.height
  );

  return canvas;
};

/**
 * Resizes a canvas to the specified dimensions
 * @param {HTMLCanvasElement} canvas - The source canvas
 * @param {number} maxSize - The maximum size (width/height)
 * @returns {HTMLCanvasElement} - The resized canvas
 */
export const resizeCanvas = (canvas, maxSize) => {
  const { width, height } = canvas;
  
  // If image is already smaller than maxSize, return as is
  if (width <= maxSize && height <= maxSize) {
    return canvas;
  }

  const scale = maxSize / Math.max(width, height);
  const newWidth = width * scale;
  const newHeight = height * scale;

  const resizedCanvas = document.createElement('canvas');
  const ctx = resizedCanvas.getContext('2d');

  if (!ctx) {
    throw new Error('No 2d context');
  }

  resizedCanvas.width = newWidth;
  resizedCanvas.height = newHeight;

  ctx.drawImage(canvas, 0, 0, newWidth, newHeight);
  return resizedCanvas;
};

/**
 * Converts canvas to File object
 * @param {HTMLCanvasElement} canvas - The canvas to convert
 * @param {string} fileName - The file name
 * @param {string} type - The MIME type (default: 'image/jpeg')
 * @param {number} quality - The image quality (0-1, default: 0.9)
 * @returns {Promise<File>} - The converted file
 */
export const canvasToFile = (canvas, fileName, type = 'image/jpeg', quality = 0.9) => {
  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      const file = new File([blob], fileName, { type });
      resolve(file);
    }, type, quality);
  });
};

/**
 * Creates an image element from a file
 * @param {File} file - The image file
 * @returns {Promise<HTMLImageElement>} - The loaded image
 */
export const createImageFromFile = (file) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Process image: crop to square and resize if needed
 * @param {File} file - The original image file
 * @param {Object} cropData - The crop data from react-easy-crop
 * @param {number} maxSize - Maximum size in pixels (default: 100)
 * @returns {Promise<File>} - The processed file
 */
export const processImage = async (file, cropData, maxSize = 100) => {
  try {
    // Create image from file
    const img = await createImageFromFile(file);
    
    // Create cropped canvas
    const croppedCanvas = createCroppedCanvas(img, cropData);
    
    // Resize if needed
    const resizedCanvas = resizeCanvas(croppedCanvas, maxSize);
    
    // Convert back to file
    const processedFile = await canvasToFile(
      resizedCanvas, 
      `cropped_${file.name}`, 
      file.type || 'image/jpeg',
      0.9
    );
    
    // Clean up object URLs
    URL.revokeObjectURL(img.src);
    
    return processedFile;
  } catch (error) {
    console.error('Error processing image:', error);
    throw error;
  }
};
