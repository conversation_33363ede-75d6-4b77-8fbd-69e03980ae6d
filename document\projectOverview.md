# YEZHome Project Documentation

**YEZHome** is a comprehensive real estate platform for buying, selling, and renting properties in Vietnam. Built with modern web technologies, it provides an intuitive interface for property discovery, user management, and transaction handling.

## Table of Contents
1. [Project Overview](#project-overview)
2. [Project Structure](#project-structure)
3. [Technology Stack](#technology-stack)
4. [Core Features](#core-features)
5. [Component Architecture](#component-architecture)
6. [Authentication & Authorization](#authentication--authorization)
7. [Internationalization](#internationalization)
8. [State Management](#state-management)
9. [API Integration](#api-integration)
10. [UI Components](#ui-components)
11. [Utilities and Helpers](#utilities-and-helpers)
12. [Development Workflow](#development-workflow)
13. [Deployment](#deployment)

## Project Overview

YEZHome is a Next.js-based real estate application that serves the Vietnamese market. The platform enables users to:
- Browse and search properties with advanced filtering
- View properties on interactive maps using Goong Maps
- Manage user accounts with authentication and profiles
- Handle property listings and management
- Process payments and wallet transactions
- Receive notifications and manage favorites
- Access the platform in both Vietnamese and English

## Project Structure

```
YEZHome_FE/
├── app/                          # Next.js App Router
│   ├── [locale]/                 # Internationalized routes
│   │   ├── (auth)/              # Authentication pages (login, register)
│   │   ├── (protected)/         # Protected user pages
│   │   │   └── user/            # User dashboard, profile, wallet, etc.
│   │   ├── bds/                 # Property detail pages
│   │   ├── tin-tuc/             # News/blog pages
│   │   ├── layout.jsx           # Root layout with providers
│   │   └── page.jsx             # Homepage with property search
│   ├── actions/                 # Server actions
│   │   └── server/              # Server-side business logic
│   ├── api/                     # API routes
│   │   ├── auth/                # Authentication endpoints
│   │   ├── map/                 # Map data endpoints
│   │   ├── payment/             # Payment processing
│   │   └── uploadthing/         # File upload handling
│   ├── globals.css              # Global styles
│   └── services/                # External service integrations
├── components/                   # React components
│   ├── auth/                    # Authentication components
│   ├── dashboard/               # Dashboard widgets
│   ├── favorite/                # Favorite property components
│   ├── layout/                  # Layout components (Navbar, Footer)
│   ├── property/                # Property-related components
│   ├── ui/                      # Reusable UI components (shadcn/ui)
│   ├── user-property/           # User property management
│   └── wallet/                  # Wallet and payment components
├── contexts/                    # React contexts
│   ├── AlertContext.jsx         # Global alert system
│   └── AuthContext.jsx          # Authentication state
├── hooks/                       # Custom React hooks
├── i18n/                        # Internationalization config
├── lib/                         # Utility libraries
│   ├── constants/               # Application constants
│   ├── schemas/                 # Zod validation schemas
│   ├── apiUtils.js              # API utility functions
│   ├── sessionUtils.js          # Session management
│   └── utils.js                 # General utilities
├── messages/                    # Translation files
│   ├── en.json                  # English translations
│   └── vi.json                  # Vietnamese translations
├── public/                      # Static assets
├── middleware.jsx               # Next.js middleware for auth & i18n
├── next.config.mjs              # Next.js configuration
└── package.json                 # Dependencies and scripts
```

## Technology Stack

### Core Technologies
- **Next.js 15.5.0** - React framework with App Router and server-side rendering
- **React 19.1.1** - Frontend library for building user interfaces
- **TypeScript 5.8.2** - Type-safe JavaScript development
- **Tailwind CSS 4.1.11** - Utility-first CSS framework

### UI Libraries & Components
- **shadcn/ui** - Modern component library built on Radix UI
- **Radix UI** - Unstyled, accessible UI primitives
- **Lucide React** - Beautiful & consistent icon library
- **Framer Motion** - Animation library for React
- **React Hook Form** - Performant forms with easy validation

### Maps & Location Services
- **Goong Maps** - Vietnamese mapping service and geocoding
- **@goongmaps/goong-js** - JavaScript SDK for Goong Maps integration

### Authentication & Security
- **JWT (jsonwebtoken)** - Token-based authentication
- **jose** - JavaScript Object Signing and Encryption
- **js-cookie** - Cookie handling for client-side storage

### Internationalization
- **next-intl** - Internationalization for Next.js applications
- **Supports Vietnamese (vi) and English (en)** - Full bilingual support

### File Upload & Media
- **UploadThing** - File upload service integration
- **React Dropzone** - Drag & drop file uploads
- **React Easy Crop** - Image cropping functionality
- **React Photo Album** - Photo gallery components

### Form Validation & Data Handling
- **Zod** - TypeScript-first schema validation
- **React Number Format** - Number input formatting
- **libphonenumber-js** - Phone number parsing and validation

### Development Tools
- **ESLint** - Code linting and quality checks
- **PostCSS** - CSS processing and optimization
- **Docker** - Containerization for deployment

## Core Features

### 1. Property Search & Discovery
- **Homepage Search Interface** - Advanced property search with filters
- **Interactive Map View** - Properties displayed on Goong Maps with markers
- **Property Listings** - Grid/list view with pagination
- **Property Details** - Comprehensive property information modal
- **Favorites System** - Save and manage favorite properties

### 2. User Authentication & Profiles
- **Registration/Login** - Email-based authentication with JWT tokens
- **User Profiles** - Comprehensive user profile management
- **Protected Routes** - Middleware-based route protection
- **Session Management** - Secure session handling with cookies

### 3. Property Management
- **Property Listing Creation** - Multi-step property posting process
- **Image Upload & Management** - Multiple image upload with cropping
- **Property Status Management** - Draft, active, expired status handling
- **Property Analytics** - View counts and performance metrics

### 4. Wallet & Payment System
- **Digital Wallet** - User balance management
- **Top-up Functionality** - Multiple payment methods (Banking, MoMo, VietQR)
- **Transaction History** - Detailed transaction records
- **Payment Processing** - Integration with Vietnamese payment gateways

### 5. Dashboard & Analytics
- **User Dashboard** - Overview of user activities and statistics
- **Property Statistics** - Performance metrics for listed properties
- **Member Ranking System** - User tier system based on activity
- **Recent Transactions** - Financial activity overview

### 6. Notification System
- **Real-time Notifications** - User activity and system notifications
- **Categorized Notifications** - Listing, Finance, Promotion, Account categories
- **Notification Bell** - Header notification indicator

## Component Architecture

### Layout Components
1. **Navbar.jsx** - Main navigation with authentication state
2. **Footer.jsx** - Site footer with links and information
3. **LeftSideNav.jsx** - User dashboard sidebar navigation
4. **AlertPopup.jsx** - Global alert/confirmation dialogs

### Property Components
1. **SearchFilter.jsx** - Advanced property search filters
2. **PropertyList.jsx** - Property listing display with pagination
3. **PropertyCard.jsx** - Individual property card component
4. **PropertyDetailModal.jsx** - Detailed property information modal
5. **HomeMap.jsx** - Interactive map with property markers
6. **PropertyImageGallery.jsx** - Image carousel and lightbox

### Authentication Components
1. **LoginDialog.jsx** - Modal login form
2. **LoginForm.jsx** - Login form with validation

### Dashboard Components
1. **WalletInfo.jsx** - Wallet balance and information display
2. **PropertyStats.jsx** - Property performance statistics
3. **RecentTransactions.jsx** - Recent financial transactions
4. **MemberRanking.jsx** - User ranking and tier information

### User Property Management
1. **PropertyBasicInfoSection.jsx** - Basic property information form
2. **PropertyImageUploader.jsx** - Image upload and management
3. **LocationSelector.jsx** - Address and location selection
4. **PricingDialog.jsx** - Property pricing configuration

## Authentication & Authorization

### Authentication Flow
1. **JWT Token-based Authentication** - Secure token storage in HTTP-only cookies
2. **Server-side Token Validation** - Middleware validates tokens on protected routes
3. **Automatic Token Refresh** - Handles token expiration gracefully
4. **Session Management** - Persistent login state across browser sessions

### Protected Routes
- `/user/*` - All user dashboard and profile pages
- Middleware automatically redirects unauthenticated users to login
- Route protection based on JWT token validation

### User Roles & Permissions
- **Buyer** - Can browse, favorite, and contact property owners
- **Seller** - Can post properties, manage listings, and receive inquiries
- Role-based UI rendering and feature access

## Internationalization

### Multi-language Support
- **Vietnamese (vi)** - Default language
- **English (en)** - Secondary language
- **next-intl Integration** - Server-side and client-side translations

### Translation Management
- **JSON-based Translations** - Separate files for each language
- **Nested Translation Keys** - Organized by feature/component
- **Dynamic Language Switching** - Runtime language changes
- **URL-based Locale Routing** - `/vi/` and `/en/` prefixes

### Implementation Details
- Middleware handles locale detection and routing
- Server-side message loading for optimal performance
- Client-side provider for component translations

## UI Components

### Base Components (shadcn/ui)
- **Button** - Various button styles and states
- **Input** - Text input with validation states
- **Select** - Dropdown selection component
- **Popover** - Floating content containers
- **Tooltip** - Hover information displays
- **Dialog** - Modal dialogs and confirmations
- **Sheet** - Slide-out panels and drawers
- **Badge** - Status and category indicators
- **Separator** - Visual content dividers

### Form Components
- **Radio Group** - Single selection from options
- **Checkbox** - Multiple selection inputs
- **Label** - Form field labels with accessibility
- **Slider** - Range input controls
- **Textarea** - Multi-line text input
- **Switch** - Toggle controls

### Layout Components
- **Avatar** - User profile images
- **Card** - Content containers with headers
- **Tabs** - Tabbed content organization
- **Dropdown Menu** - Context menus and actions
- **Collapsible** - Expandable content sections
- **Scroll Area** - Custom scrollable containers

### Data Display Components
- **Table** - Data tables with sorting
- **Pagination** - Page navigation controls
- **Progress** - Progress indicators
- **Skeleton** - Loading state placeholders
- **Alert** - Status messages and notifications

### Custom Components
- **GoongMap** - Vietnamese map integration
- **ImageCard** - Property image displays
- **ImageCropModal** - Image editing functionality
- **LanguageSwitcher** - Language selection
- **LoadingSpinner** - Custom loading indicators

## State Management

### Context-based State Management
1. **AuthContext** - Global authentication state management
   - User login/logout state
   - User profile information
   - Token validation and refresh
   - Session persistence

2. **AlertContext** - Global alert and notification system
   - Modal alerts and confirmations
   - Error message display
   - User feedback notifications

### Local Component State
- **React useState** - Component-level state management
- **React Hook Form** - Form state and validation
- **Custom Hooks** - Reusable stateful logic (usePropertyList, useDebounce)

### Server State Management
- **Server Actions** - Next.js server actions for data mutations
- **API Routes** - RESTful endpoints for data fetching
- **Optimistic Updates** - Immediate UI updates with server sync
- **Error Handling** - Comprehensive error states and user feedback

### Data Flow Patterns
- **Prop Drilling** - For shallow component hierarchies
- **Context Providers** - For global state (auth, alerts)
- **Server Actions** - For form submissions and mutations
- **URL State** - Search parameters for filters and pagination

## API Integration

### Server Actions Architecture
- **Authentication Actions** (`app/actions/server/authenticate.jsx`)
  - User login, registration, password management
  - JWT token handling and validation
  - Session management

- **Property Actions** (`app/actions/server/property.jsx`)
  - Property CRUD operations
  - Property search and filtering
  - Image upload and management
  - Property status updates

- **Wallet Actions** (`app/actions/server/wallet.jsx`)
  - Wallet balance retrieval
  - Transaction history
  - Top-up request creation
  - Payment processing

- **User Actions** (`app/actions/server/user.jsx`)
  - User profile management
  - Dashboard data aggregation
  - User preferences and settings

### API Routes
- **Authentication Endpoints** (`app/api/auth/`)
- **Map Data Endpoints** (`app/api/map/`)
- **Payment Processing** (`app/api/payment/`)
- **File Upload Handling** (`app/api/uploadthing/`)

### External API Integration
- **Backend API** - RESTful API communication
- **Goong Maps API** - Vietnamese mapping services
- **Payment Gateways** - Vietnamese payment providers
- **UploadThing** - File upload service

## Utilities and Helpers

### Core Utilities (`lib/utils.js`)
- **cn()** - Tailwind CSS class merging utility
- **parseEmptyStringsToNull()** - Data sanitization
- **formatCurrency()** - Vietnamese currency formatting
- **debounce()** - Function debouncing for performance
- **normalizeVNPhoneNumber()** - Vietnamese phone number formatting

### API Utilities (`lib/apiUtils.js`)
- **handleErrorResponse()** - Standardized error handling
- **logError()** - Centralized error logging

### Session Management (`lib/sessionUtils.js`)
- **createSession()** - Secure session creation
- **getSession()** - Session retrieval
- **deleteSession()** - Session cleanup
- **fetchWithAuth()** - Authenticated API requests
- **verifyJwtToken()** - JWT token validation

### Data Utilities (`lib/dataUtils.js`)
- **formatPropertyData()** - Property data transformation
- **validateFormData()** - Form validation helpers

### Image Utilities (`lib/imageUtils.js`)
- **optimizeImage()** - Image optimization
- **generateThumbnail()** - Thumbnail creation

### Custom Hooks
- **use-toast** - Toast notification system
- **use-mobile** - Mobile device detection
- **useDebounce** - Debounced value hook
- **usePropertyList** - Property list management

### Constants and Enums (`lib/enum.js`)
- **PropertyType** - Property type definitions
- **PostType** - Listing type definitions
- **PropertyStatus** - Status enumerations
- **PaymentMethods** - Payment method configurations
- **TransactionStatus** - Transaction state definitions

### Validation Schemas (`lib/schemas/`)
- **authSchema.jsx** - Authentication form validation
- **propertyFormSchema.jsx** - Property form validation
- Zod-based type-safe validation

## Development Workflow

### Getting Started
1. **Prerequisites**
   - Node.js (Latest LTS version)
   - npm or yarn package manager
   - Git for version control

2. **Installation**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Environment Configuration**
   - Copy `env.example` to `.env.local`
   - Configure required environment variables:
     - `API_URL` - Backend API endpoint
     - `NEXT_PUBLIC_API_URL` - Public API endpoint
     - `NEXTAUTH_SECRET` - Authentication secret
     - `GOONG_API_KEY` - Goong Maps API key

4. **Development Server**
   ```bash
   npm run dev
   # Runs on https://localhost:3000 with experimental HTTPS
   ```

### Code Standards
- **TypeScript** - Type-safe development
- **ESLint** - Code linting and formatting
- **Prettier** - Code formatting (if configured)
- **Component Structure** - Organized by feature/domain
- **Naming Conventions**:
  - PascalCase for components
  - camelCase for functions and variables
  - kebab-case for file names (when appropriate)

### Git Workflow
- Feature branch development
- Descriptive commit messages
- Pull request reviews
- Automated testing (if configured)

## Deployment

### Production Build
```bash
npm run build
npm start
```

### Docker Deployment
- **Dockerfile** - Production container configuration
- **docker-compose.prod.yml** - Production orchestration
- **docker-compose.staging.yml** - Staging environment
- **Nginx Configuration** - Reverse proxy and SSL termination

### Environment-specific Configurations
- **Production** - Optimized builds with caching
- **Staging** - Testing environment with production-like setup
- **Development** - Hot reloading and debugging tools

### SSL/HTTPS Setup
- Local development HTTPS support
- Production SSL certificate management
- Nginx SSL configuration

## Performance Considerations

### Optimization Strategies
- **Next.js App Router** - Server-side rendering and static generation
- **Image Optimization** - Next.js Image component with optimization
- **Code Splitting** - Dynamic imports for large components
- **Lazy Loading** - Deferred loading of non-critical components
- **Debouncing** - Search input optimization
- **Caching** - API response caching where appropriate

### Bundle Analysis
- Use `npm run build` to analyze bundle sizes
- Monitor Core Web Vitals
- Optimize images and assets

### SEO Optimization
- Server-side rendering for better SEO
- Meta tags and structured data
- Internationalized URLs
- Sitemap generation (if implemented)

## Architecture Decisions

### Why Next.js App Router?
- **Server Components** - Better performance with server-side rendering
- **Nested Layouts** - Efficient layout composition
- **Built-in Internationalization** - Native i18n support
- **API Routes** - Full-stack capabilities in one framework

### Why Goong Maps?
- **Vietnamese Market Focus** - Optimized for Vietnamese addresses
- **Local Data Accuracy** - Better local business and location data
- **Cost Effective** - More affordable than Google Maps for Vietnamese market
- **Government Compliance** - Meets local regulatory requirements

### Why JWT + Cookies?
- **Security** - HTTP-only cookies prevent XSS attacks
- **Scalability** - Stateless authentication
- **Performance** - No server-side session storage required
- **Mobile Compatibility** - Works across web and mobile platforms

### Why Server Actions?
- **Type Safety** - Full TypeScript support
- **Performance** - Reduced client-side JavaScript
- **Security** - Server-side validation and processing
- **Developer Experience** - Simplified data mutations

## Key Technical Highlights

### 1. Bilingual Architecture
- Complete Vietnamese and English support
- URL-based locale routing (`/vi/`, `/en/`)
- Server-side translation loading for performance
- Dynamic language switching without page reload

### 2. Advanced Property Search
- Multi-criteria filtering (location, price, type, features)
- Real-time search with debouncing
- Map-based property discovery
- URL state persistence for shareable searches

### 3. Comprehensive User Management
- Role-based access control (Buyer/Seller)
- Protected route middleware
- User profile management
- Activity tracking and analytics

### 4. Payment Integration
- Multiple Vietnamese payment methods
- Secure transaction processing
- Digital wallet system
- Transaction history and reporting

### 5. Modern Development Stack
- TypeScript for type safety
- Server-side rendering for performance
- Component-based architecture
- Responsive design with Tailwind CSS

## Future Enhancements

### Planned Features
- **Mobile Application** - React Native or Flutter app
- **Real-time Chat** - Property inquiry messaging
- **Advanced Analytics** - Property market insights
- **AI-powered Recommendations** - Personalized property suggestions
- **Virtual Tours** - 360° property viewing
- **Mortgage Calculator** - Financial planning tools

### Technical Improvements
- **Testing Suite** - Unit and integration tests
- **Performance Monitoring** - Real-time performance tracking
- **CDN Integration** - Global content delivery
- **Progressive Web App** - Offline capabilities
- **Microservices Architecture** - Service decomposition for scalability

---

*This documentation reflects the current state of the YEZHome frontend application as of the latest update. For the most current information, please refer to the codebase and recent commit history.*
