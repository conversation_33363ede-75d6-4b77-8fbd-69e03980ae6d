"use client";

import Image from "next/image";

export default function CustomPhotoGallery({ 
  images = [], 
  propertyName = "", 
  onImageClick 
}) {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 sm:gap-3 md:gap-4">
      {images.map((src, index) => (
        <div
          key={index}
          className="relative aspect-[4/3] overflow-hidden rounded-lg cursor-pointer group"
          onClick={() => onImageClick(index)}
        >
          <Image
            src={src || "/placeholder.svg"}
            alt={`${propertyName || "Property"} image ${index + 1}`}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
            sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw"
          />
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
        </div>
      ))}
    </div>
  );
}
